package com.lysjk.utils;

import org.mindrot.jbcrypt.BCrypt;

/**
 * BCrypt 密码加密工具类
 * 提供密码加密、验证和自动升级功能
 */
public final class BCryptUtil {
    // 默认加密强度（数值越大越安全但性能越低，建议 10-14）
    private static final int DEFAULT_STRENGTH = 12;

    // 私有构造函数，防止实例化
    private BCryptUtil() {
    }

    /**
     * 加密明文密码
     *
     * @param plaintextPassword 明文密码
     * @return 加密后的密码（包含 salt）
     */
    public static String encrypt(String plaintextPassword) {
        if (plaintextPassword == null) {
            throw new IllegalArgumentException("Password cannot be null");
        }
        return BCrypt.hashpw(plaintextPassword, BCrypt.gensalt(DEFAULT_STRENGTH));
    }

    /**
     * 验证密码是否匹配
     *
     * @param plaintextPassword 明文密码
     * @param hashedPassword    加密后的密码
     * @return true-匹配；false-不匹配
     */
    public static boolean verify(String plaintextPassword, String hashedPassword) {
        if (plaintextPassword == null || hashedPassword == null) {
            return false;
        }
        return BCrypt.checkpw(plaintextPassword, hashedPassword);
    }

    /**
     * 检查密码是否需要重新加密（例如加密强度提高时）
     *
     * @param hashedPassword 已加密的密码
     * @return true-需要重新加密；false-不需要
     */
    public static boolean needsRehash(String hashedPassword) {
        if (hashedPassword == null) {
            return false;
        }
        // 提取当前加密版本和强度
        String[] parts = hashedPassword.split("\\$");
        if (parts.length < 4) {
            return false;
        }
        int currentStrength = Integer.parseInt(parts[2]);
        return currentStrength < DEFAULT_STRENGTH;
    }

    /**
     * 升级密码加密强度
     *
     * @param plaintextPassword 明文密码
     * @param oldHashedPassword 旧的加密密码
     * @return 新的加密密码（如果需要升级），否则返回原密码
     */
    public static String upgradeIfNeeded(String plaintextPassword, String oldHashedPassword) {
        if (needsRehash(oldHashedPassword)) {
            return encrypt(plaintextPassword);
        }
        return oldHashedPassword;
    }
}