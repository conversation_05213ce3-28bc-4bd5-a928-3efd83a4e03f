package com.lysjk.utils;

import java.util.HashMap;
import java.util.Map;

/**
 * 操作名称工具类
 * 根据类名和方法名推断操作名称
 */
public class OperationNameUtil {
    
    // 预定义的操作名称映射
    private static final Map<String, String> OPERATION_NAME_MAP = new HashMap<>();
    
    static {
        // 用户相关操作
        OPERATION_NAME_MAP.put("UsersController.login", "用户登录");
        OPERATION_NAME_MAP.put("UsersController.register", "管理员注册用户");
        OPERATION_NAME_MAP.put("UsersController.userInfo", "获取用户信息");
        OPERATION_NAME_MAP.put("UsersController.selectAll", "查询所有用户");
        OPERATION_NAME_MAP.put("UsersController.update", "更新用户信息");
        OPERATION_NAME_MAP.put("UsersController.updatePwd", "修改密码");
        OPERATION_NAME_MAP.put("UsersController.updateUserPwd", "管理员重置密码");
        OPERATION_NAME_MAP.put("UsersController.deleteUser", "删除用户");
        OPERATION_NAME_MAP.put("UsersController.updateUser", "管理员更新用户");
        OPERATION_NAME_MAP.put("UsersController.selectByUsernameLike", "模糊查询用户");
        OPERATION_NAME_MAP.put("UsersController.selectPage", "分页查询用户");
        
        // 文件相关操作
        OPERATION_NAME_MAP.put("FileController.fileUpload", "文件上传");
        OPERATION_NAME_MAP.put("FileController.downloadFile", "文件下载");
        OPERATION_NAME_MAP.put("FileController.downloadFileAsBytes", "文件下载(字节流)");
        OPERATION_NAME_MAP.put("FileController.deleteFile", "删除文件");

        // 地物信息表相关操作
        OPERATION_NAME_MAP.put("TRegionInfoController.selectById", "查询地物信息");
        OPERATION_NAME_MAP.put("TRegionInfoController.selectByAdministrativeRegion", "按行政区划查询地物");
        OPERATION_NAME_MAP.put("TRegionInfoController.selectCenterById", "查询地物中心点");
        OPERATION_NAME_MAP.put("TRegionInfoController.selectRegionById", "查询地物区域范围");
        OPERATION_NAME_MAP.put("TRegionInfoController.insert", "新增地物信息");
        OPERATION_NAME_MAP.put("TRegionInfoController.deleteById", "删除地物信息");
        OPERATION_NAME_MAP.put("TRegionInfoController.updateCenterById", "更新地物中心点");
        OPERATION_NAME_MAP.put("TRegionInfoController.updateRegionById", "更新地物区域范围");
        OPERATION_NAME_MAP.put("TRegionInfoController.updateBusinessFields", "更新地物业务信息");
        OPERATION_NAME_MAP.put("TRegionInfoController.selectPage", "分页查询地物信息");
        OPERATION_NAME_MAP.put("TRegionInfoController.selectRegionInfoWithMonitoringPoints", "查询地物信息及其关联的监测点列表");
        OPERATION_NAME_MAP.put("TRegionInfoController.selectRegionInfoWithMonitoringPointsById", "根据地物ID查询地物信息及其关联的监测点列表");
        OPERATION_NAME_MAP.put("TRegionInfoController.selectRegionInfoWithMonitoringPointsPage", "分页查询地物信息及其关联的监测点列表");

        // 监测点信息表相关操作
        OPERATION_NAME_MAP.put("TMonitoringPointController.selectById", "根据ID查询监测点信息");
        OPERATION_NAME_MAP.put("TMonitoringPointController.insertMonitoringPoint", "新增监测点信息");
        OPERATION_NAME_MAP.put("TMonitoringPointController.deleteById", "根据Id删除监测点信息");
        OPERATION_NAME_MAP.put("TMonitoringPointController.updateBusinessFields", "更新监测点业务信息");
        OPERATION_NAME_MAP.put("TMonitoringPointController.selectPage", "分页查询监测点信息");
        OPERATION_NAME_MAP.put("TMonitoringPointController.selectByCondition", "条件查询监测点信息");
        OPERATION_NAME_MAP.put("TMonitoringPointController.selectPageByCondition", "分页条件查询监测点信息");
        OPERATION_NAME_MAP.put("TMonitoringPointController.selectByNameLike", "根据名称模糊查询");
        OPERATION_NAME_MAP.put("TMonitoringPointController.selectByLocationLike", "根据地理位置模糊查询");
        OPERATION_NAME_MAP.put("TMonitoringPointController.selectByRegionId", "根据地物ID查询监测点信息");

        // 水域环境信息相关操作
        OPERATION_NAME_MAP.put("TWaterEnvironmentController.save", "新增水域环境信息");
        OPERATION_NAME_MAP.put("TWaterEnvironmentController.delete", "删除水域环境信息");
        OPERATION_NAME_MAP.put("TWaterEnvironmentController.update", "更新水域环境信息");
        OPERATION_NAME_MAP.put("TWaterEnvironmentController.selectPage", "分页查询水域环境信息");
    }
    
    /**
     * 根据类名和方法名获取操作名称
     * @param className 完整类名
     * @param methodName 方法名
     * @return 操作名称
     */
    public static String getOperationName(String className, String methodName) {
        // 提取简单类名
        String simpleClassName = getSimpleClassName(className);
        String key = simpleClassName + "." + methodName;
        
        // 先从预定义映射中查找
        String operationName = OPERATION_NAME_MAP.get(key);
        if (operationName != null) {
            return operationName;
        }
        
        // 如果没有预定义，则根据方法名推断
        return inferOperationNameFromMethod(methodName);
    }
    
    /**
     * 从完整类名中提取简单类名
     */
    private static String getSimpleClassName(String fullClassName) {
        if (fullClassName == null) {
            return "";
        }
        int lastDotIndex = fullClassName.lastIndexOf('.');
        return lastDotIndex >= 0 ? fullClassName.substring(lastDotIndex + 1) : fullClassName;
    }
    
    /**
     * 根据方法名推断操作名称
     */
    private static String inferOperationNameFromMethod(String methodName) {
        if (methodName == null) {
            return "未知操作";
        }
        
        String lowerMethodName = methodName.toLowerCase();
        
        // 登录相关
        if (lowerMethodName.contains("login")) {
            return "用户登录";
        }
        if (lowerMethodName.contains("logout")) {
            return "用户登出";
        }
        if (lowerMethodName.contains("register")) {
            return "用户注册";
        }
        
        // 查询相关
        if (lowerMethodName.contains("select") || lowerMethodName.contains("find") || 
            lowerMethodName.contains("get") || lowerMethodName.contains("query")) {
            if (lowerMethodName.contains("all")) {
                return "查询所有数据";
            }
            if (lowerMethodName.contains("page")) {
                return "分页查询";
            }
            if (lowerMethodName.contains("like")) {
                return "模糊查询";
            }
            return "查询数据";
        }
        
        // 新增相关
        if (lowerMethodName.contains("insert") || lowerMethodName.contains("add") || 
            lowerMethodName.contains("create") || lowerMethodName.contains("save")) {
            return "新增数据";
        }
        
        // 更新相关
        if (lowerMethodName.contains("update") || lowerMethodName.contains("modify") || 
            lowerMethodName.contains("edit")) {
            if (lowerMethodName.contains("pwd") || lowerMethodName.contains("password")) {
                return "修改密码";
            }
            return "更新数据";
        }
        
        // 删除相关
        if (lowerMethodName.contains("delete") || lowerMethodName.contains("remove")) {
            return "删除数据";
        }
        
        // 文件相关
        if (lowerMethodName.contains("upload")) {
            return "文件上传";
        }
        if (lowerMethodName.contains("download")) {
            return "文件下载";
        }
        
        // 导入导出
        if (lowerMethodName.contains("export")) {
            return "数据导出";
        }
        if (lowerMethodName.contains("import")) {
            return "数据导入";
        }
        
        // 默认返回方法名
        return methodName;
    }
    
    /**
     * 添加自定义操作名称映射
     * @param className 类名
     * @param methodName 方法名
     * @param operationName 操作名称
     */
    public static void addOperationNameMapping(String className, String methodName, String operationName) {
        String key = getSimpleClassName(className) + "." + methodName;
        OPERATION_NAME_MAP.put(key, operationName);
    }
}
