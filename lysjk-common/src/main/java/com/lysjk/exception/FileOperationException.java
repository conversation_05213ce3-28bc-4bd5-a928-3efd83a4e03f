package com.lysjk.exception;

/**
 * 文件操作异常类
 * 用于处理文件上传、下载、删除等操作中的异常
 */
public class FileOperationException extends CustomException {
    
    public FileOperationException(String msg) {
        super(500, msg);
    }
    
    public FileOperationException(String msg, Throwable cause) {
        super(500, msg, cause);
    }
    
    /**
     * 文件不存在异常
     */
    public static class FileNotFoundException extends FileOperationException {
        public FileNotFoundException(String fileName) {
            super("文件不存在: " + fileName);
        }
    }
    
    /**
     * 文件上传异常
     */
    public static class FileUploadException extends FileOperationException {
        public FileUploadException(String msg) {
            super("文件上传失败: " + msg);
        }
        
        public FileUploadException(String msg, Throwable cause) {
            super("文件上传失败: " + msg, cause);
        }
    }
    
    /**
     * 文件下载异常
     */
    public static class FileDownloadException extends FileOperationException {
        public FileDownloadException(String msg) {
            super("文件下载失败: " + msg);
        }
        
        public FileDownloadException(String msg, Throwable cause) {
            super("文件下载失败: " + msg, cause);
        }
    }
    
    /**
     * 文件类型不支持异常
     */
    public static class UnsupportedFileTypeException extends FileOperationException {
        public UnsupportedFileTypeException(String fileType) {
            super("不支持的文件类型: " + fileType);
        }
    }
    
    /**
     * 文件大小超限异常
     */
    public static class FileSizeExceededException extends FileOperationException {
        public FileSizeExceededException(long maxSize) {
            super("文件大小超过限制: " + maxSize + " bytes");
        }
    }

    /**
     * 文件导出异常
     */
    public static class FileExportException extends FileOperationException {
        public FileExportException(Throwable cause) {
            super("文件导出失败: " + cause);
        }

        public FileExportException(String msg, Throwable cause) {
            super("文件导出失败: " + msg, cause);
        }
    }

    /**
     * 文件导入异常
     */
    public static class FileImportException extends FileOperationException {
        public FileImportException(Throwable cause) {
            super("文件导入失败: " + cause);
        }

        public FileImportException(String msg, Throwable cause) {
            super("文件导入失败: " + msg, cause);
        }
    }
}
