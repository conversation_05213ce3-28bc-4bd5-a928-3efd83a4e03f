package com.lysjk.exception;

/**
 * 数据验证异常类
 * 用于处理参数验证、数据格式验证等异常
 */
public class ValidationException extends CustomException {
    
    public ValidationException(String msg) {
        super(400, msg);
    }
    
    public ValidationException(String msg, Throwable cause) {
        super(400, msg, cause);
    }
    
    /**
     * 参数为空异常
     */
    public static class ParameterNullException extends ValidationException {
        public ParameterNullException(String parameterName) {
            super("参数不能为空: " + parameterName);
        }
    }
    
    /**
     * 参数格式错误异常
     */
    public static class ParameterFormatException extends ValidationException {
        public ParameterFormatException(String parameterName, String expectedFormat) {
            super("参数格式错误: " + parameterName + ", 期望格式: " + expectedFormat);
        }
    }
    
    /**
     * 参数值超出范围异常
     */
    public static class ParameterOutOfRangeException extends ValidationException {
        public ParameterOutOfRangeException(String parameterName, String range) {
            super("参数值超出范围: " + parameterName + ", 有效范围: " + range);
        }
    }
    
    /**
     * 数据重复异常
     */
    public static class DuplicateDataException extends ValidationException {
        public DuplicateDataException(String dataType, String value) {
            super(dataType + "已存在: " + value);
        }
    }

    /**
     * 数据不存在异常
     */
    public static class DataNotFoundException extends ValidationException {
        public DataNotFoundException(String dataType) {
            super(dataType + "不存在");
        }

        public DataNotFoundException(String dataType, String identifier) {
            super(dataType + "不存在: " + identifier);
        }
    }
}
