package com.lysjk.exception;

/**
 * 认证相关异常类
 * 用于处理登录、权限验证等认证相关的异常
 */
public class AuthenticationException extends CustomException {

    public AuthenticationException(int code, String msg) {
        super(code, msg);
    }

    public AuthenticationException(int code, String msg, Throwable cause) {
        super(code, msg, cause);
    }

    /**
     * 用户名或密码错误异常 - 401未认证
     */
    public static class InvalidCredentialsException extends AuthenticationException {
        public InvalidCredentialsException() {
            super(401, "用户名或密码错误");
        }
    }

    /**
     * Token无效异常 - 401未认证
     */
    public static class InvalidTokenException extends AuthenticationException {
        public InvalidTokenException() {
            super(401, "Token无效或已过期");
        }
    }

    /**
     * 用户不存在异常 - 401未认证
     */
    public static class UserNotFoundException extends AuthenticationException {
        public UserNotFoundException(String username) {
            super(401, "用户不存在: " + username);
        }
    }

    /**
     * 用户已存在异常 - 400错误请求
     */
    public static class UserAlreadyExistsException extends AuthenticationException {
        public UserAlreadyExistsException(String username) {
            super(400, "用户已存在: " + username);
        }
    }

    /**
     * 权限不足异常 - 403禁止访问
     */
    public static class InsufficientPermissionException extends AuthenticationException {
        public InsufficientPermissionException() {
            super(403, "权限不足");
        }

        public InsufficientPermissionException(String operation) {
            super(403, "权限不足，无法执行操作: " + operation);
        }
    }
}
