package com.lysjk.exception;

/**
 * 通用业务异常类
 * 用于处理各种业务逻辑异常
 */
public class BusinessException extends CustomException {
    
    public BusinessException(String msg) {
        super(msg);
    }
    
    public BusinessException(Integer code, String msg) {
        super(code, msg);
    }
    
    public BusinessException(String msg, Throwable cause) {
        super(msg, cause);
    }
    
    public BusinessException(Integer code, String msg, Throwable cause) {
        super(code, msg, cause);
    }
    
    /**
     * 操作失败异常
     */
    public static class OperationFailedException extends BusinessException {
        public OperationFailedException(String operation) {
            super("操作失败: " + operation);
        }
        
        public OperationFailedException(String operation, String reason) {
            super("操作失败: " + operation + ", 原因: " + reason);
        }
    }
    
    /**
     * 数据处理异常
     */
    public static class DataProcessException extends BusinessException {
        public DataProcessException(String msg) {
            super("数据处理失败: " + msg);
        }
        
        public DataProcessException(String msg, Throwable cause) {
            super("数据处理失败: " + msg, cause);
        }
    }
    
    /**
     * 系统配置异常
     */
    public static class SystemConfigException extends BusinessException {
        public SystemConfigException(String msg) {
            super("系统配置错误: " + msg);
        }
    }
}
