package com.lysjk.enums;

/**
 * 操作类型枚举
 */
public enum OperationType {
    SELECT("SELECT", "查询"),
    POST("POST", "新增"),
    PUT("PUT", "更新"),
    DELETE("DELETE", "删除"),
    LOGIN("LOGIN", "登录"),
    LOGOUT("LOGOUT", "登出"),
    UPLOAD("UPLOAD", "上传"),
    DOWNLOAD("DOWNLOAD", "下载"),
    EXPORT("EXPORT", "导出"),
    IMPORT("IMPORT", "导入"),
    OTHER("OTHER", "其他");
    
    private final String code;
    private final String description;
    
    OperationType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据方法名推断操作类型
     */
    public static OperationType inferFromMethodName(String methodName) {
        if (methodName == null) {
            return OTHER;
        }
        
        String lowerMethodName = methodName.toLowerCase(); // 将方法名转换为小写，方便后续匹配
        
        // 查询操作
        if (lowerMethodName.contains("select") || lowerMethodName.contains("find") || 
            lowerMethodName.contains("get") || lowerMethodName.contains("query") ||
            lowerMethodName.contains("list") || lowerMethodName.contains("search")) {
            return SELECT;
        }
        
        // 新增操作
        if (lowerMethodName.contains("insert") || lowerMethodName.contains("add") || 
            lowerMethodName.contains("create") || lowerMethodName.contains("save") ||
            lowerMethodName.contains("register")) {
            return POST;
        }
        
        // 更新操作
        if (lowerMethodName.contains("update") || lowerMethodName.contains("modify") || 
            lowerMethodName.contains("edit") || lowerMethodName.contains("change")) {
            return PUT;
        }
        
        // 删除操作
        if (lowerMethodName.contains("delete") || lowerMethodName.contains("remove") || 
            lowerMethodName.contains("drop")) {
            return DELETE;
        }
        
        // 登录操作
        if (lowerMethodName.contains("login") || lowerMethodName.contains("signin")) {
            return LOGIN;
        }
        
        // 登出操作
        if (lowerMethodName.contains("logout") || lowerMethodName.contains("signout")) {
            return LOGOUT;
        }
        
        // 上传操作
        if (lowerMethodName.contains("upload")) {
            return UPLOAD;
        }
        
        // 下载操作
        if (lowerMethodName.contains("download")) {
            return DOWNLOAD;
        }
        
        // 导出操作
        if (lowerMethodName.contains("export")) {
            return EXPORT;
        }
        
        // 导入操作
        if (lowerMethodName.contains("import")) {
            return IMPORT;
        }
        
        return OTHER;
    }
}
