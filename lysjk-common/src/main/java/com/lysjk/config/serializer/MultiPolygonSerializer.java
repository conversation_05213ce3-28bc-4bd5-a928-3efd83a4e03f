package com.lysjk.config.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.MultiPolygon;
import org.locationtech.jts.geom.Polygon;
import org.locationtech.jts.io.WKTWriter;

import java.io.IOException;

/**
 * MultiPolygon类型的JSON序列化器
 * 将MultiPolygon对象序列化为包含type、wkt、srid和coordinates的对象
 * coordinates是GeoJSON-like的嵌套数组，便于前端解析；如果只需WKT，可配置省略coordinates
 */
public class MultiPolygonSerializer extends JsonSerializer<MultiPolygon> {

    private static final WKTWriter WKT_WRITER = new WKTWriter();

    @Override
    public void serialize(MultiPolygon multiPolygon, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (multiPolygon == null) {
            gen.writeNull();
            return;
        }

        gen.writeStartObject();
        gen.writeStringField("type", "MultiPolygon");
        gen.writeStringField("wkt", WKT_WRITER.write(multiPolygon));
        if (multiPolygon.getSRID() != 0) {
            gen.writeNumberField("srid", multiPolygon.getSRID());
        }

        // 写入坐标数组（经纬度）
        gen.writeFieldName("coordinates");
        gen.writeStartArray();

        for (int i = 0; i < multiPolygon.getNumGeometries(); i++) {
            Polygon polygon = (Polygon) multiPolygon.getGeometryN(i);
            gen.writeStartArray();

            // 外环坐标
            writeRingCoordinates(gen, polygon.getExteriorRing().getCoordinates());

            // 内环坐标
            for (int j = 0; j < polygon.getNumInteriorRing(); j++) {
                writeRingCoordinates(gen, polygon.getInteriorRingN(j).getCoordinates());
            }

            gen.writeEndArray();
        }

        gen.writeEndArray();
        gen.writeEndObject();
    }

    private void writeRingCoordinates(JsonGenerator gen, Coordinate[] coordinates) throws IOException {
        gen.writeStartArray();
        for (Coordinate coord : coordinates) {
            gen.writeStartArray();
            gen.writeNumber(coord.x); // longitude
            gen.writeNumber(coord.y); // latitude
            gen.writeEndArray();
        }
        gen.writeEndArray();
    }
}

//package com.lysjk.config.serializer;
//
//import com.fasterxml.jackson.core.JsonGenerator;
//import com.fasterxml.jackson.databind.JsonSerializer;
//import com.fasterxml.jackson.databind.SerializerProvider;
//import org.locationtech.jts.geom.Coordinate;
//import org.locationtech.jts.geom.MultiPolygon;
//import org.locationtech.jts.geom.Polygon;
//import org.locationtech.jts.io.WKTWriter;
//
//import java.io.IOException;
//
///**
// * MultiPolygon类型的JSON序列化器
// * 将MultiPolygon对象序列化为简单的坐标数组，避免深度嵌套
// */
//public class MultiPolygonSerializer extends JsonSerializer<MultiPolygon> {
//
//    private static final WKTWriter WKT_WRITER = new WKTWriter();
//
//    @Override
//    public void serialize(MultiPolygon multiPolygon, JsonGenerator gen, SerializerProvider serializers) throws IOException {
//        if (multiPolygon == null) {
//            gen.writeNull();
//            return;
//        }
//
//        gen.writeStartObject();
//        gen.writeStringField("type", "MultiPolygon");
//        gen.writeStringField("wkt", WKT_WRITER.write(multiPolygon));
//        if (multiPolygon.getSRID() != 0) {
//            gen.writeNumberField("srid", multiPolygon.getSRID());
//        }
//
//        // 写入坐标数组
//        gen.writeFieldName("coordinates");
//        gen.writeStartArray();
//
//        for (int i = 0; i < multiPolygon.getNumGeometries(); i++) {
//            Polygon polygon = (Polygon) multiPolygon.getGeometryN(i);
//            gen.writeStartArray();
//
//            // 外环坐标
//            gen.writeStartArray();
//            Coordinate[] coordinates = polygon.getExteriorRing().getCoordinates();
//            for (Coordinate coord : coordinates) {
//                gen.writeStartArray();
//                gen.writeNumber(coord.x);
//                gen.writeNumber(coord.y);
//                gen.writeEndArray();
//            }
//            gen.writeEndArray();
//
//            // 内环坐标（如果有的话）
//            for (int j = 0; j < polygon.getNumInteriorRing(); j++) {
//                gen.writeStartArray();
//                Coordinate[] holeCoords = polygon.getInteriorRingN(j).getCoordinates();
//                for (Coordinate coord : holeCoords) {
//                    gen.writeStartArray();
//                    gen.writeNumber(coord.x);
//                    gen.writeNumber(coord.y);
//                    gen.writeEndArray();
//                }
//                gen.writeEndArray();
//            }
//
//            gen.writeEndArray();
//        }
//
//        gen.writeEndArray();
//        gen.writeEndObject();
//    }
//}
