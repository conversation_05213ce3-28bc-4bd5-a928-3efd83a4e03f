package com.lysjk.config.serializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.lysjk.utils.GeometryUtil;
import org.locationtech.jts.geom.*;
import org.locationtech.jts.io.WKTReader;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * MultiPolygon类型的JSON反序列化器
 * 支持WKT字符串、对象中的wkt字段，或GeoJSON-like的coordinates数组
 */
public class MultiPolygonDeserializer extends JsonDeserializer<MultiPolygon> {

    private static final GeometryFactory GEOMETRY_FACTORY = new GeometryFactory();
    private static final WKTReader WKT_READER = new WKTReader();

    @Override
    public MultiPolygon deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonNode node = p.getCodec().readTree(p);

        if (node.isNull()) {
            return null;
        }

        // 如果是字符串，尝试解析为WKT
        if (node.isTextual()) {
            String wkt = node.asText();
            return GeometryUtil.createMultiPolygonFromWKT(wkt);
        }

        // 如果是对象，从WKT字段创建MultiPolygon
        if (node.isObject()) {
            if (node.has("wkt")) {
                String wkt = node.get("wkt").asText();
                return GeometryUtil.createMultiPolygonFromWKT(wkt);
            } else if (node.has("coordinates")) {
                // 支持从coordinates数组解析（GeoJSON风格）
                return createMultiPolygonFromCoordinates(node.get("coordinates"));
            }
        }

        throw new IOException("无法解析MultiPolygon对象，预期格式：WKT字符串或包含'wkt'/'coordinates'的对象: " + node.toString());
    }

    private MultiPolygon createMultiPolygonFromCoordinates(JsonNode coordinatesNode) throws IOException {
        if (!coordinatesNode.isArray()) {
            throw new IOException("coordinates必须是数组");
        }

        List<Polygon> polygons = new ArrayList<>();
        for (JsonNode polyNode : coordinatesNode) {
            if (!polyNode.isArray()) continue;

            // 外环
            JsonNode exteriorNode = polyNode.get(0);
            LinearRing exterior = createRingFromArray(exteriorNode);

            // 内环
            List<LinearRing> interiors = new ArrayList<>();
            for (int i = 1; i < polyNode.size(); i++) {
                interiors.add(createRingFromArray(polyNode.get(i)));
            }

            Polygon polygon = GEOMETRY_FACTORY.createPolygon(exterior, interiors.toArray(new org.locationtech.jts.geom.LinearRing[0]));
            polygons.add(polygon);
        }

        return GEOMETRY_FACTORY.createMultiPolygon(polygons.toArray(new Polygon[0]));
    }

    private LinearRing createRingFromArray(JsonNode ringNode) throws IOException {
        if (!ringNode.isArray()) {
            throw new IOException("环坐标必须是数组");
        }

        List<Coordinate> coords = new ArrayList<>();
        for (JsonNode coordNode : ringNode) {
            if (coordNode.isArray() && coordNode.size() >= 2) {
                double x = coordNode.get(0).asDouble();
                double y = coordNode.get(1).asDouble();
                coords.add(new Coordinate(x, y));
            }
        }

        // 确保闭合
        if (!coords.get(0).equals(coords.get(coords.size() - 1))) {
            coords.add(coords.get(0));
        }

        return GEOMETRY_FACTORY.createLinearRing(coords.toArray(new Coordinate[0]));
    }
}

//package com.lysjk.config.serializer;
//
//import com.fasterxml.jackson.core.JsonParser;
//import com.fasterxml.jackson.databind.DeserializationContext;
//import com.fasterxml.jackson.databind.JsonDeserializer;
//import com.fasterxml.jackson.databind.JsonNode;
//import com.lysjk.utils.GeometryUtil;
//import org.locationtech.jts.geom.MultiPolygon;
//
//import java.io.IOException;
//
///**
// * MultiPolygon类型的JSON反序列化器
// */
//public class MultiPolygonDeserializer extends JsonDeserializer<MultiPolygon> {
//
//    @Override
//    public MultiPolygon deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
//        JsonNode node = p.getCodec().readTree(p);
//
//        if (node.isNull()) {
//            return null;
//        }
//
//        // 如果是字符串，尝试解析为WKT
//        if (node.isTextual()) {
//            String wkt = node.asText();
//            return GeometryUtil.createMultiPolygonFromWKT(wkt);
//        }
//
//        // 如果是对象，从WKT字段创建MultiPolygon
//        if (node.isObject() && node.has("wkt")) {
//            String wkt = node.get("wkt").asText();
//            return GeometryUtil.createMultiPolygonFromWKT(wkt);
//        }
//
//        throw new IOException("无法解析MultiPolygon对象: " + node.toString());
//    }
//}
