package com.lysjk.config.serializer;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.lysjk.utils.GeometryUtil;
import org.locationtech.jts.geom.Point;

import java.io.IOException;

/**
 * Point类型的JSON反序列化器
 * 支持WKT字符串、对象中的wkt，或longitude/latitude (或x/y兼容)
 */
public class PointDeserializer extends JsonDeserializer<Point> {

    @Override
    public Point deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonNode node = p.getCodec().readTree(p);

        if (node.isNull()) {
            return null;
        }

        // 如果是字符串，尝试解析为WKT
        if (node.isTextual()) {
            String wkt = node.asText();
            return GeometryUtil.createPointFromWKT(wkt);
        }

        // 如果是对象，从坐标或WKT创建Point
        if (node.isObject()) {
            if (node.has("wkt")) {
                String wkt = node.get("wkt").asText();
                return GeometryUtil.createPointFromWKT(wkt);
            } else if (node.has("longitude") && node.has("latitude")) {
                double longitude = node.get("longitude").asDouble();
                double latitude = node.get("latitude").asDouble();
                return GeometryUtil.createPoint(longitude, latitude);
            }
        }

        throw new IOException("无法解析Point对象，预期格式：WKT字符串或包含'wkt'/'longitude'+'latitude'/'x'+'y'的对象: " + node.toString());
    }
}

//package com.lysjk.config.serializer;
//
//import com.fasterxml.jackson.core.JsonParser;
//import com.fasterxml.jackson.databind.DeserializationContext;
//import com.fasterxml.jackson.databind.JsonDeserializer;
//import com.fasterxml.jackson.databind.JsonNode;
//import com.lysjk.utils.GeometryUtil;
//import org.locationtech.jts.geom.Point;
//
//import java.io.IOException;
//
///**
// * Point类型的JSON反序列化器
// */
//public class PointDeserializer extends JsonDeserializer<Point> {
//
//    @Override
//    public Point deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
//        JsonNode node = p.getCodec().readTree(p);
//
//        if (node.isNull()) {
//            return null;
//        }
//
//        // 如果是字符串，尝试解析为WKT
//        if (node.isTextual()) {
//            String wkt = node.asText();
//            return GeometryUtil.createPointFromWKT(wkt);
//        }
//
//        // 如果是对象，从坐标创建Point
//        if (node.isObject()) {
//            if (node.has("wkt")) {
//                String wkt = node.get("wkt").asText();
//                return GeometryUtil.createPointFromWKT(wkt);
//            } /*else if (node.has("x") && node.has("y")) {
//                double x = node.get("x").asDouble();
//                double y = node.get("y").asDouble();
//                return GeometryUtil.createPoint(x, y);
//            } */else if (node.has("longitude") && node.has("latitude")) {
//                double longitude = node.get("longitude").asDouble();
//                double latitude = node.get("latitude").asDouble();
//                return GeometryUtil.createPoint(longitude, latitude);
//            }
//        }
//
//        throw new IOException("无法解析Point对象: " + node.toString());
//    }
//}
