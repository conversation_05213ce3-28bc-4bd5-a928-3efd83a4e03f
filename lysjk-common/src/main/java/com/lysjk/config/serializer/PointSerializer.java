package com.lysjk.config.serializer;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.io.WKTWriter;

import java.io.IOException;

/**
 * Point类型的JSON序列化器
 * 将Point对象序列化为包含type、longitude、latitude、wkt和srid的对象
 */
public class PointSerializer extends JsonSerializer<Point> {

    private static final WKTWriter WKT_WRITER = new WKTWriter();

    @Override
    public void serialize(Point point, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (point == null) {
            gen.writeNull();
            return;
        }

        gen.writeStartObject();
        gen.writeStringField("type", "Point");
        gen.writeNumberField("longitude", point.getX());
        gen.writeNumberField("latitude", point.getY());
        gen.writeStringField("wkt", WKT_WRITER.write(point));
        if (point.getSRID() != 0) {
            gen.writeNumberField("srid", point.getSRID());
        }
        gen.writeEndObject();
    }
}

//package com.lysjk.config.serializer;
//
//import com.fasterxml.jackson.core.JsonGenerator;
//import com.fasterxml.jackson.databind.JsonSerializer;
//import com.fasterxml.jackson.databind.SerializerProvider;
//import org.locationtech.jts.geom.Point;
//import org.locationtech.jts.io.WKTWriter;
//
//import java.io.IOException;
//
///**
// * Point类型的JSON序列化器
// * 将Point对象序列化为简单的坐标对象，避免深度嵌套
// */
//public class PointSerializer extends JsonSerializer<Point> {
//
//    private static final WKTWriter WKT_WRITER = new WKTWriter();
//
//    @Override
//    public void serialize(Point point, JsonGenerator gen, SerializerProvider serializers) throws IOException {
//        if (point == null) {
//            gen.writeNull();
//            return;
//        }
//
//        gen.writeStartObject();
//        gen.writeStringField("type", "Point");
////        gen.writeNumberField("x", point.getX());
////        gen.writeNumberField("y", point.getY());
//        gen.writeNumberField("longitude", point.getX());
//        gen.writeNumberField("latitude", point.getY());
//        gen.writeStringField("wkt", WKT_WRITER.write(point));
//        if (point.getSRID() != 0) {
//            gen.writeNumberField("srid", point.getSRID());
//        }
//        gen.writeEndObject();
//    }
//}
