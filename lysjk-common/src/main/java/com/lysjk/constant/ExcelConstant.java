package com.lysjk.constant;

import com.lysjk.exception.CustomException;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * Excel导出的文件名称
 */
public class ExcelConstant {
    public static final String EXPORT_EXCEPTION = "文件名称编码异常";
    public static final String USER_EXPORT_FILE_NAME;

    static {
        try {
            USER_EXPORT_FILE_NAME = URLEncoder.encode("用户信息表", "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new CustomException(EXPORT_EXCEPTION);
        }
    }
}
