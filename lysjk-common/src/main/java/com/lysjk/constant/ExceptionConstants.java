package com.lysjk.constant;

/**
 * 异常信息常量,到时候可以进行扩充
 */
public class ExceptionConstants {
    // 认证相关异常
    public static final String INVALID_CREDENTIALS = "用户名或密码错误";
    public static final String INVALID_TOKEN = "Token无效或已过期";
    public static final String USER_NOT_FOUND = "用户不存在";
    public static final String USER_ALREADY_EXISTS = "用户已存在";
    public static final String INSUFFICIENT_PERMISSION = "权限不足";
    public static final String ACCOUNT_LOCKED = "账户已被锁定";
    public static final String ACCOUNT_DISABLED = "账户已被禁用";

    // 业务相关异常
    public static final String PARAM_ERROR = "参数错误";
    public static final String DATA_NOT_FOUND = "数据不存在";
    public static final String OPERATION_FAILED = "操作失败";
    public static final String DUPLICATE_DATA = "数据重复";

    // HTTP状态码
    public static final Integer SUCCESS = 200;
    public static final Integer BAD_REQUEST = 400;
    public static final Integer UNAUTHORIZED = 401;
    public static final Integer FORBIDDEN = 403;
    public static final Integer NOT_FOUND = 404;
    public static final Integer INTERNAL_SERVER_ERROR = 500;
}
