package com.lysjk.anno;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义注解，用于标识哪些方法需要记录操作日志
 * 支持自定义操作名称和操作类型
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface LogOperation {

    /**
     * 操作名称，如"用户登录"、"文件上传"、"用户注册"等
     * 如果不指定，将根据方法名自动推断
     */
    String operationName() default "";

    /**
     * 操作类型，如"SELECT"、"INSERT"、"UPDATE"、"DELETE"等
     * 如果不指定，将根据方法名自动推断
     */
    String operationType() default "";
}