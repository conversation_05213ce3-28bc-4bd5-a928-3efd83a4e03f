package com.lysjk.anno;


import com.lysjk.enums.Operation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 自定义注解,主要是用来让公共字段自动填充
 */
@Target(ElementType.METHOD) // 主要是标识在mapper中的方法上,因为对数据库统一处理
@Retention(RetentionPolicy.RUNTIME)
public @interface AutoFill {
    // 只有一个value,可以省略
    // 而且使用枚举类感觉一举俩得,限制了类型必须是我定义的,符号规范
    // 当然String 也行,不过少了一层限制,用自定义的static final字符串来赋值
    Operation value(); // 定义的类型是枚举Operation,所以填写的内容不能为简单的字符串
}
