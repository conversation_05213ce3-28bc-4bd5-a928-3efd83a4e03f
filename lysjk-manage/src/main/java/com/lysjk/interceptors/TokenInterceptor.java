package com.lysjk.interceptors;

import com.lysjk.utils.JwtUtil;
import com.lysjk.utils.ThreadLocalUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.Map;

@Slf4j
@Component // 注入ioc容器
public class TokenInterceptor implements HandlerInterceptor {

    private static final String TOKEN_NAME = "token"; // 令牌名称

    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        /**
         *  首先获取的token肯定在请求头中,然后再解析token
         *  也应该配合线程ThreadLocal使用,因为更安全,就相当于深拷贝,不同对象获取的内容应该刚好不冲突
         */
        String token = request.getHeader(TOKEN_NAME); // 设置前端传递过来的名称
        // 验证token
        try {
            // 第二步从redis中获取相同的token
            Map<String, Object> claims = JwtUtil.parseToken(token);
            Integer tokenId = (Integer) claims.get("uid"); // 存储的时候值为整型,所以不能直接String转换
            ValueOperations<String, String> operations = stringRedisTemplate.opsForValue(); // 操作集合对象
            String redisToken =  operations.get(tokenId.toString()); // 看看是否能获取浏览器的token
            if(redisToken == null || !redisToken.equals(token)){
                // token已经失效
                throw new RuntimeException(); // 这样就会在下方catch被捕捉到
            }
            ThreadLocalUtil.set(claims);
            return true; // 放行
        } catch (Exception e) {
            // 设置401状态码，表示未认证
            response.setStatus(401);
            // 确保响应体为空，不返回任何内容
            response.getWriter().flush();
            return false; // 此时不是error因为拦截器,所以false就是不放行
        }
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 清除ThreadLocal中的数据
        ThreadLocalUtil.remove();
    }
}
