package com.lysjk.controller;

import com.lysjk.anno.LogOperation;
import com.lysjk.dto.TRegionInfoPageQueryDTO;
import com.lysjk.dto.TRegionInfoWithMonitoringPointsDTO;
import com.lysjk.result.PageResult;
import com.lysjk.result.Result;
import com.lysjk.entity.TRegionInfo;
import com.lysjk.service.TRegionInfoService;
import com.lysjk.exception.ValidationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 地物信息controller层
 */
@Slf4j
@RestController
@RequestMapping("/regionInfo")
public class TRegionInfoController {

    @Autowired
    private TRegionInfoService tRegionInfoService;

    /**
     * 根据主键查询地物信息
     */
    @LogOperation(operationName = "查询地物信息")
    @GetMapping("/{id}")
    public Result<TRegionInfo> selectById(@PathVariable Integer id) {
        if (id == null) {
            throw new ValidationException.ParameterNullException("地物ID");
        }
        TRegionInfo regionInfo = tRegionInfoService.selectByPrimaryKey(id);
        if (regionInfo != null) {
            return Result.success(regionInfo);
        } else {
            throw new ValidationException.DataNotFoundException("地物信息", id.toString());
        }
    }

    /**
     * 分页条件查询地物信息
     * 可以根据多个条件进行分页查询
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    @GetMapping("/page")
    @LogOperation(operationName = "分页查询地物信息")
    public Result<PageResult> selectPage(TRegionInfoPageQueryDTO pageQueryDTO) {
        log.info("分页查询地物信息: {}", pageQueryDTO);
        PageResult pageResult = tRegionInfoService.selectPage(pageQueryDTO);
        return Result.success(pageResult);
    }

    /**
     * 查询地物信息及其关联的监测点列表
     */
    @LogOperation(operationName = "查询地物信息及关联监测点")
    @GetMapping("/withMonitoringPoints")
    public Result<List<TRegionInfoWithMonitoringPointsDTO>> selectRegionInfoWithMonitoringPoints() {
        List<TRegionInfoWithMonitoringPointsDTO> result = tRegionInfoService.selectRegionInfoWithMonitoringPoints();
        return Result.success(result);
    }

    /**
     * 根据地物ID查询地物信息及其关联的监测点列表
     */
    @LogOperation(operationName = "查询指定地物信息及关联监测点")
    @GetMapping("/{id}/withMonitoringPoints")
    public Result<List<TRegionInfoWithMonitoringPointsDTO>> selectRegionInfoWithMonitoringPointsById(@PathVariable Integer id) {
        if (id == null) {
            throw new ValidationException.ParameterNullException("地物ID");
        }
        List<TRegionInfoWithMonitoringPointsDTO> result = tRegionInfoService.selectRegionInfoWithMonitoringPointsById(id);
        if (result != null) {
            return Result.success(result);
        } else {
            throw new ValidationException.DataNotFoundException("地物信息", id.toString());
        }
    }
}
