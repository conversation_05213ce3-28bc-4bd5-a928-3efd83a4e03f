package com.lysjk.controller;

import com.github.pagehelper.PageInfo;
import com.lysjk.anno.LogOperation;
import com.lysjk.constant.RoleConstant;
import com.lysjk.dto.TRegionInfoWithMonitoringPointsDTO;
import com.lysjk.exception.AuthenticationException;
import com.lysjk.result.Result;
import com.lysjk.entity.TRegionInfo;
import com.lysjk.service.TRegionInfoService;
import com.lysjk.exception.ValidationException;
import com.lysjk.exception.BusinessException;
import com.lysjk.utils.ThreadLocalUtil;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.MultiPolygon;
import org.locationtech.jts.geom.Point;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 地物信息controller层
 */
@Slf4j
@RestController
@RequestMapping("/regionInfo")
public class TRegionInfoController {
    
    @Autowired
    private TRegionInfoService tRegionInfoService;

    /**
     * 根据主键查询地物信息
     */
    @LogOperation
    @GetMapping("/{id}")
    public Result<TRegionInfo> selectById(@PathVariable Integer id) {
        if (id == null) {
            throw new ValidationException.ParameterNullException("地物ID");
        }
        TRegionInfo regionInfo = tRegionInfoService.selectByPrimaryKey(id);
        if (regionInfo != null) {
            return Result.success(regionInfo);
        } else {
            throw new ValidationException.DataNotFoundException("地物信息", id.toString());
        }
    }
    
    /**
     * 根据行政区划条件查询地物信息
     */
    @LogOperation
    @GetMapping("/searchByRegion")
    public Result<List<TRegionInfo>> selectByAdministrativeRegion(@RequestParam(required = false) String sheng,
                                               @RequestParam(required = false) String shi,
                                               @RequestParam(required = false) String qu,
                                               @RequestParam(required = false) String zhen) {
        List<TRegionInfo> regionInfoList = tRegionInfoService.selectByAdministrativeRegion(sheng, shi, qu, zhen);
        return Result.success(regionInfoList);
    }
    
    /**
     * 根据ID查询中心点坐标
     */
    @LogOperation
    @GetMapping("/{id}/center")
    public Result<String> selectCenterById(@PathVariable Integer id) {
        try {
            Point center = tRegionInfoService.selectCenterById(id);
            if (center != null) {
                return Result.success(center.toString());
            } else {
                return Result.error("中心点坐标不存在");
            }
        } catch (Exception e) {
            log.error("查询中心点坐标失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据ID查询区域范围
     */
    @LogOperation
    @GetMapping("/{id}/region")
    public Result<String> selectRegionById(@PathVariable Integer id) {
        if (id == null) {
            throw new ValidationException.ParameterNullException("地物ID");
        }
        MultiPolygon region = tRegionInfoService.selectRegionById(id);
        if (region != null) {
            return Result.success(region.toString());
        } else {
            throw new ValidationException.DataNotFoundException("区域范围", id.toString());
        }
    }
    
    /**
     * 新增地物信息
     */
    @LogOperation
    @PostMapping
    public Result insert(@RequestBody TRegionInfo record) {
        if (record == null) {
            throw new ValidationException.ParameterNullException("地物信息");
        }

        // 检查地物编码唯一性
        if (record.getCode() != null && !record.getCode().trim().isEmpty()) {
            boolean isUnique = tRegionInfoService.isCodeUnique(record.getCode(), null);
            if (!isUnique) {
                throw new ValidationException.DuplicateDataException("地物编码", record.getCode());
            }
        }

        int result = tRegionInfoService.insertSelective(record);
        if (result > 0) {
            return Result.success();
        } else {
            throw new BusinessException.OperationFailedException("新增地物信息");
        }
    }
    
    /**
     * 根据主键删除地物信息
     */
    @LogOperation
    @DeleteMapping("/{id}")
    public Result deleteById(@PathVariable Integer id) {
        Map<String, Object> claims = ThreadLocalUtil.get();
        String role = (String) claims.get(RoleConstant.ROLE);
        if(!role.equals(RoleConstant.ADMIN)){
            throw new AuthenticationException.InsufficientPermissionException();
        }
        if (id == null) {
            throw new ValidationException.ParameterNullException("地物ID");
        }
        int result = tRegionInfoService.deleteByPrimaryKey(id);
        if (result > 0) {
            return Result.success();
        } else {
            throw new ValidationException.DataNotFoundException("地物信息", id.toString());
        }
    }
    
    /**
     * 更新中心点坐标
     */
    @LogOperation
    @PutMapping("/{id}/center")
    public Result updateCenterById(@PathVariable Integer id, @RequestBody Point center) {
        if (id == null) {
            throw new ValidationException.ParameterNullException("地物ID");
        }
        if (center == null) {
            throw new ValidationException.ParameterNullException("中心点坐标");
        }
        int result = tRegionInfoService.updateCenterById(id, center);
        if (result > 0) {
            return Result.success();
        } else {
            throw new ValidationException.DataNotFoundException("地物信息", id.toString());
        }
    }
    
    /**
     * 更新区域范围
     */
    @LogOperation
    @PutMapping("/{id}/region")
    public Result updateRegionById(@PathVariable Integer id, @RequestBody MultiPolygon region) {
        try {
            int result = tRegionInfoService.updateRegionById(id, region);
            if (result > 0) {
                return Result.success();
            } else {
                return Result.error("更新区域范围失败");
            }
        } catch (Exception e) {
            log.error("更新区域范围失败", e);
            return Result.error("更新失败：" + e.getMessage());
        }
    }
    
    /**
     * 更新业务字段（排除系统字段）
     */
    @LogOperation
    @PutMapping("/{id}/business")
    public Result updateBusinessFields(@PathVariable Integer id, @RequestBody TRegionInfo record) {
        if (id == null) {
            throw new ValidationException.ParameterNullException("地物ID");
        }
        if (record == null) {
            throw new ValidationException.ParameterNullException("地物信息");
        }

        record.setId(id); // 确保ID正确

        // 检查地物编码唯一性（排除当前记录）
        if (record.getCode() != null && !record.getCode().trim().isEmpty()) {
            boolean isUnique = tRegionInfoService.isCodeUnique(record.getCode(), id);
            if (!isUnique) {
                throw new ValidationException.DuplicateDataException("地物编码", record.getCode());
            }
        }

        int result = tRegionInfoService.updateBusinessFields(record);
        if (result > 0) {
            return Result.success();
        } else {
            throw new ValidationException.DataNotFoundException("地物信息", id.toString());
        }
    }

    /**
     * 分页查询地物信息
     */
    @LogOperation
    @GetMapping("/selectPage")
    public Result<PageInfo<TRegionInfo>> selectPage(@RequestParam(defaultValue = "1") int pageNum,
                            @RequestParam(defaultValue = "10") int pageSize) {
        if (pageNum < 1) {
            throw new ValidationException.ParameterOutOfRangeException("页码", "大于0");
        }
        if (pageSize < 1 || pageSize > 100) {
            throw new ValidationException.ParameterOutOfRangeException("页面大小", "1-100");
        }
        PageInfo<TRegionInfo> pageInfo = tRegionInfoService.selectPage(pageNum, pageSize);
        return Result.success(pageInfo);
    }

    /**
     * 查询地物信息及其关联的监测点列表
     */
    @LogOperation
    @GetMapping("/withMonitoringPoints")
    public Result<List<TRegionInfoWithMonitoringPointsDTO>> selectRegionInfoWithMonitoringPoints() {
        List<TRegionInfoWithMonitoringPointsDTO> result = tRegionInfoService.selectRegionInfoWithMonitoringPoints();
        return Result.success(result);
    }

    /**
     * 根据地物ID查询地物信息及其关联的监测点列表
     */
    @LogOperation
    @GetMapping("/{id}/withMonitoringPoints")
    public Result<List<TRegionInfoWithMonitoringPointsDTO>> selectRegionInfoWithMonitoringPointsById(@PathVariable Integer id) {
        if (id == null) {
            throw new ValidationException.ParameterNullException("地物ID");
        }
        List<TRegionInfoWithMonitoringPointsDTO> result = tRegionInfoService.selectRegionInfoWithMonitoringPointsById(id);
        if (result != null) {
            return Result.success(result);
        } else {
            throw new ValidationException.DataNotFoundException("地物信息", id.toString());
        }
    }

    /**
     * 分页查询地物信息及其关联的监测点列表
     */
    @LogOperation
    @GetMapping("/withMonitoringPoints/selectPage")
    public Result<PageInfo<TRegionInfoWithMonitoringPointsDTO>> selectRegionInfoWithMonitoringPointsPage(@RequestParam(defaultValue = "1") int pageNum,
                                                          @RequestParam(defaultValue = "10") int pageSize) {
        if (pageNum < 1) {
            throw new ValidationException.ParameterOutOfRangeException("页码", "大于0");
        }
        if (pageSize < 1 || pageSize > 100) {
            throw new ValidationException.ParameterOutOfRangeException("页面大小", "1-100");
        }
        PageInfo<TRegionInfoWithMonitoringPointsDTO> pageInfo = tRegionInfoService.selectRegionInfoWithMonitoringPointsPage(pageNum, pageSize);
        return Result.success(pageInfo);
    }
}
