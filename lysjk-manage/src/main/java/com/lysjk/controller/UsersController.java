package com.lysjk.controller;

import com.github.pagehelper.PageInfo;
import com.lysjk.anno.LogOperation;
import com.lysjk.constant.PasswordConstant;
import com.lysjk.constant.RoleConstant;
import com.lysjk.entity.Users;
import com.lysjk.exception.AuthenticationException;
import com.lysjk.exception.ValidationException;
import com.lysjk.result.Result;
import com.lysjk.service.UsersService;
import com.lysjk.utils.BCryptUtil;
import com.lysjk.utils.ThreadLocalUtil;
import com.lysjk.vo.UsersVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

// 普通用户可以增删改查自己信息,但不能删除自己的账号,只有管理员可以删除
@RestController // Rest所以是json字符串传递给浏览器
@RequestMapping("/user")
@Validated // 和pattern配合参数校验
public class UsersController {

    @Autowired
    private UsersService usersService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @LogOperation(operationType = "SELECT")
    @GetMapping("/userInfo") // 也可以用HttpServletRequest request 然后request.getHeader,不过注解肯定更省事
    public Result<Users> userInfo(/*@RequestHeader(name = "token") String token*/){
        // 根据用户名查询用户,因为是存储的值是Object类型,所以要单独强转
/*        Map<String, Object> claims = JwtUtil.parseToken(token); // 内部还有一个get对象
        String username = (String) claims.get("username"); // 这个就是进一步获取键值对*/
        Map<String,Object> map = ThreadLocalUtil.get();
        String username = (String) map.get("username");
        Users users = usersService.selectByUsername(username); // 对象
        return Result.success(users);
        // 不过在拦截器如果成功时就已经写过一次了,所以我们要减少重复才行
    }

    @LogOperation
    @GetMapping("/selectAll")
    public Result<List<Users>> selectAll(){
        List<Users> usersList = new ArrayList<>();
        usersList = usersService.selectAll();
        return Result.success(usersList);
    }

    // 必须加上RequestBody才能成功修改
    @LogOperation
    @PutMapping("/update")
    public Result update(@RequestBody Users users){
        Map<String,Object> map = ThreadLocalUtil.get();
        Integer id = (Integer) map.get("uid");
        users.setUid(id);
        usersService.update(users);
        return Result.success();
    }

    // 更新后需要跳转到登录页面,因为此时token已经失效
    @LogOperation(operationType = "PATCH")
    @PatchMapping("/updatePwd") // 加上注解mvc才能自动读取前端数据并转换为json对象
    public Result updatePwd(@RequestBody Map<String,String> params/*, @RequestHeader("token") String token*/){
        // 1.手动校验参数,因为目前注解无法直接获取
        // 没有手写实体类,所以只接受前端传来的请求数据
        String oldPwd = params.get("oldPwd");
        String newPwd = params.get("newPwd");
        String rePwd = params.get("rePwd");

        // StringUtils.hasLength 方法为开发者提供了一种安全、便捷的方式来判断字符串是否有长度，尤其在处理可能为 null 的字符串时非常有用
        if(!StringUtils.hasLength(oldPwd) || !StringUtils.hasLength(newPwd) || !StringUtils.hasLength(rePwd)){
            throw new ValidationException.ParameterNullException("密码信息");
        }

        // 原密码是否正确
        Map<String, Object> map = ThreadLocalUtil.get();
        Users loginUsers = usersService.selectByUsername((String) map.get("username"));
        Integer tokenId = (Integer) map.get("uid");
        if(!BCryptUtil.verify(oldPwd, loginUsers.getPassword())){
            throw new AuthenticationException.InvalidCredentialsException();
        }

//        if(newPwd.length() < 5 || newPwd.length() > 16){
//            return Result.error("新密码长度不够");
//        }

        // 别填写一致
        if(oldPwd.equals(newPwd)){
            throw new ValidationException.ParameterFormatException("新密码", "不能和原密码相同");
        }

        // 重复密码是否正确
        if(!rePwd.equals(newPwd)){
            throw new ValidationException.ParameterFormatException("重复密码", "两次输入的新密码必须一致");
        }

//        usersService.updatePwd(BCryptUtil.encrypt(newPwd), tokenId);
        usersService.updatePwdByUid(tokenId, BCryptUtil.encrypt(newPwd));
        // 注意ThreadLocal获取到的是解析后的token相关信息,而不是token
        // 第三步,更新密码成功后要删除redis中对应的token, @RequestHeader("Authorization") String token就是自动获取请求头相关信息
        ValueOperations<String, String> operations = stringRedisTemplate.opsForValue(); // 还是先得到操作集合
        operations.getOperations().delete(tokenId.toString());
        return Result.success();
    }

    // 只是一个重置密码按钮,可以输入账号uid来重置,因为缓存存的是uid,要不然还需要再次查询uid来删,或直接前端传递uid,username一起传
    @LogOperation(operationType = "PATCH")
    @PatchMapping("/admin/updatePwd")
    public Result updateUserPwd(Integer uid){
        Map<String,Object> map = ThreadLocalUtil.get();
        String role = (String) map.get(RoleConstant.ROLE);
        ValueOperations<String, String> operations = stringRedisTemplate.opsForValue(); // 还是先得到操作集合
        operations.getOperations().delete(uid.toString()); // 不会出现异常,结果会返回0
        if(role.equals(RoleConstant.ADMIN)){
            String encryptPwd = BCryptUtil.encrypt(PasswordConstant.DEFAULT_PASSWORD);
            usersService.updatePwdByUid(uid,encryptPwd);
            return Result.success();
        }
        throw new AuthenticationException.InsufficientPermissionException();
    }

    @LogOperation
    @DeleteMapping("/admin/deleteUser")
    public Result deleteUser(Integer uid){
        Map<String,Object> map = ThreadLocalUtil.get();
        String role = (String) map.get(RoleConstant.ROLE);
        // 只有用户这边需要删除相应的token
        ValueOperations<String, String> operations = stringRedisTemplate.opsForValue(); // 还是先得到操作集合
        operations.getOperations().delete(uid.toString()); // 不会出现异常,结果会返回0
        if(role.equals(RoleConstant.ADMIN)){
            usersService.deleteUser(uid);
            return Result.success();
        }
        throw new AuthenticationException.InsufficientPermissionException();
    }

    @LogOperation
    @PutMapping("/admin/updateUser")
    public Result updateUser(@RequestBody Users users){
        Map<String,Object> map = ThreadLocalUtil.get();
        String role = (String) map.get(RoleConstant.ROLE);
        if(role.equals(RoleConstant.ADMIN)){
            usersService.updateUser(users);
            return Result.success();
        }
        throw new AuthenticationException.InsufficientPermissionException();
    }

    /**
     * 分页查询
     * pageNum: 当前页码
     * pageSize: 每页个数
     * 有默认值就可以不写参数了,但最好还是指定一下
     */
    @LogOperation
    @GetMapping("/selectByUsernameLike")
    public Result<List<Users>> selectByUsernameLike(@RequestParam String username){
        List<Users> usersList = new ArrayList<>();
        usersList = usersService.selectByUsernameLike(username);
        return Result.success(usersList);
    }

    @LogOperation
    @GetMapping("/selectPage")
    public Result<PageInfo<Users>> selectPage(Users users,
                             @RequestParam(defaultValue = "1") Integer pageNum,
                             @RequestParam(defaultValue = "10") Integer pageSize) {
        PageInfo<Users> pageInfo = usersService.selectPage(users, pageNum, pageSize);
        return Result.success(pageInfo);
    }

    /**
     * 查询用户信息
     * @return
     */
    @GetMapping("/list")
    public Result<List<UsersVO>> list(String username, String nickname){
        List<UsersVO> usersVO = usersService.list(username, nickname);
        return Result.success(usersVO);
    }
}

