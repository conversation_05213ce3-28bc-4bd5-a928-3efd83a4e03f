package com.lysjk.controller;

import com.lysjk.anno.LogOperation;
import com.lysjk.dto.TWaterQualityPageQueryDTO;
import com.lysjk.entity.TWaterQuality;
import com.lysjk.result.PageResult;
import com.lysjk.result.Result;
import com.lysjk.service.TWaterQualityService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 水质信息控制层
 */
@RestController
@RequestMapping("/waterQuality")
@Slf4j
public class TWaterQualityController {

    @Autowired
    private TWaterQualityService waterQualityService;

    /**
     * 新增水质信息
     * @param waterQuality 水质信息
     * @return 操作结果
     */
    @PostMapping
    @LogOperation(operationName = "新增水质信息")
    public Result save(@RequestBody TWaterQuality waterQuality) {
        log.info("新增水质信息: {}", waterQuality);
        waterQualityService.save(waterQuality);
        return Result.success();
    }

    /**
     * 批量删除水质信息
     * 批量删除格式为?ids=1,2,3
     * @param ids ID列表
     * @return 操作结果
     */
    @DeleteMapping
    @LogOperation(operationName = "删除水质信息")
    public Result delete(@RequestParam List<Integer> ids) {
        log.info("批量删除水质信息: {}", ids);
        waterQualityService.deleteBatch(ids);
        return Result.success();
    }

    /**
     * 更新水质信息
     * 必传id，然后是json格式
     * @param waterQuality 水质信息
     * @return 操作结果
     */
    @PutMapping
    @LogOperation(operationName = "更新水质信息")
    public Result update(@RequestBody TWaterQuality waterQuality) {
        log.info("更新水质信息: {}", waterQuality);
        waterQualityService.update(waterQuality);
        return Result.success();
    }

    /**
     * 分页查询水质信息
     * 可以根据多个条件进行分页查询
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    @GetMapping("/page")
    @LogOperation(operationName = "分页查询水质信息")
    public Result<PageResult> selectPage(TWaterQualityPageQueryDTO pageQueryDTO) {
        log.info("分页查询水质信息: {}", pageQueryDTO);
        PageResult pageResult = waterQualityService.selectPage(pageQueryDTO);
        return Result.success(pageResult);
    }
}
