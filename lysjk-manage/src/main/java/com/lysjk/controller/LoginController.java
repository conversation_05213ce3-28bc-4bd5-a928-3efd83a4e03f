package com.lysjk.controller;

import com.lysjk.anno.LogOperation;
import com.lysjk.constant.RoleConstant;
import com.lysjk.vo.LoginInfoVO;
import com.lysjk.entity.Users;
import com.lysjk.exception.AuthenticationException;
import com.lysjk.result.Result;
import com.lysjk.service.UsersService;
import com.lysjk.utils.ThreadLocalUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Slf4j
@RestController
public class LoginController {
    @Autowired
    private UsersService usersService;

    // 登录user只接受到了前端返回的用户名和密码,也需要向前端返回token
    @LogOperation
    @PostMapping("/login")
    public Result<LoginInfoVO> login(@RequestBody Users users) {
        // 用户登录,所以service层可以封装在用户中
        LoginInfoVO loginInfoVO = usersService.login(users);
        // 判断能否获取信息
        if (loginInfoVO != null) {
            return Result.success(loginInfoVO);
        }
        throw new AuthenticationException.InvalidCredentialsException();
    }

    // 修改为只有管理员登录后可以注册新员工
    @LogOperation
    @PostMapping("/register") // 是否匹配密码
    public Result register(@RequestBody Users users) {
        Map<String, Object> claims = ThreadLocalUtil.get();
        String role = (String) claims.get(RoleConstant.ROLE);
        if(!role.equals(RoleConstant.ADMIN)){
            throw new AuthenticationException.InsufficientPermissionException();
        }
        // 查询用户
        Users usersQuery = usersService.selectByUsername(users.getUsername());
        if(usersQuery == null){
            usersService.register(users.getUsername(), users.getPassword(), users.getNickname(), users.getDefaultPro(), users.getOrganizationId());
            return Result.success();
        }
        else{
            throw new AuthenticationException.UserAlreadyExistsException(users.getUsername());
        }
    }
}
