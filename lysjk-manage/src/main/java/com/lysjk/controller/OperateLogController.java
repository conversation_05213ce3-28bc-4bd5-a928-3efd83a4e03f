package com.lysjk.controller;

import com.github.pagehelper.PageInfo;
import com.lysjk.constant.RoleConstant;
import com.lysjk.entity.OperateLog;
import com.lysjk.exception.AuthenticationException;
import com.lysjk.exception.ValidationException;
import com.lysjk.result.Result;
import com.lysjk.service.OperateLogService;
import com.lysjk.utils.ThreadLocalUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 操作日志控制层
 */
@Slf4j
@RestController
@RequestMapping("/operateLog")
public class OperateLogController {
    
    @Autowired
    private OperateLogService operateLogService;
    
    /**
     * 根据主键删除操作日志
     */
//    @LogOperation(operationName = "删除操作日志")
    @DeleteMapping("/deleteByOid")
    public Result deleteByOid(@RequestParam Integer oid) {
        Map<String, Object> claims = ThreadLocalUtil.get();
        String role = (String) claims.get(RoleConstant.ROLE);
        if(!role.equals(RoleConstant.ADMIN)){
            throw new AuthenticationException.InsufficientPermissionException();
        }
        if (oid == null) {
            throw new ValidationException.ParameterNullException("操作日志ID");
        }
        int result = operateLogService.deleteByOid(oid);
        if (result > 0) {
            return Result.success();
        } else {
            throw new ValidationException.DataNotFoundException("操作日志", oid.toString());
        }
    }
    
    /**
     * 分页查询操作日志
     */
//    @LogOperation(operationName = "分页查询操作日志")
    @GetMapping("/selectPage")
    public Result selectPage(@RequestParam(defaultValue = "1") int pageNum,
                            @RequestParam(defaultValue = "10") int pageSize) {
        PageInfo<OperateLog> pageInfo = operateLogService.selectPage(pageNum, pageSize);
        return Result.success(pageInfo);
    }
    
    /**
     * 根据条件查询操作日志（支持模糊查询）
     */
//    @LogOperation(operationName = "根据条件查询操作日志")
    @GetMapping("/selectByCondition")
    public Result<List<OperateLog>> selectByCondition(@RequestParam(required = false) String username,
                                   @RequestParam(required = false) String operationType,
                                   @RequestParam(required = false) String operationName,
                                   @RequestParam(required = false) String operationUrl) {
        List<OperateLog> logs = operateLogService.selectByCondition(username, operationType, operationName, operationUrl);
        return Result.success(logs);
    }

    /**
     * 分页条件查询操作日志
     */
//    @LogOperation(operationName = "分页条件查询操作日志")
    @GetMapping("/selectPageByCondition")
    public Result<PageInfo<OperateLog>> selectPageByCondition(@RequestParam(required = false) String username,
                                       @RequestParam(required = false) String operationType,
                                       @RequestParam(required = false) String operationName,
                                       @RequestParam(required = false) String operationUrl,
                                       @RequestParam(defaultValue = "1") int pageNum,
                                       @RequestParam(defaultValue = "10") int pageSize) {
        PageInfo<OperateLog> pageInfo = operateLogService.selectPageByCondition(
                username, operationType, operationName, operationUrl, pageNum, pageSize);
        return Result.success(pageInfo);
    }
}
