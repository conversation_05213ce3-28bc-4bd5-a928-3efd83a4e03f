package com.lysjk.controller;

import com.github.pagehelper.PageInfo;
import com.lysjk.anno.LogOperation;
import com.lysjk.constant.RoleConstant;
import com.lysjk.exception.AuthenticationException;
import com.lysjk.result.Result;
import com.lysjk.entity.TMonitoringPoint;
import com.lysjk.service.TMonitoringPointService;
import com.lysjk.exception.ValidationException;
import com.lysjk.exception.BusinessException;
import com.lysjk.utils.ThreadLocalUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 监测点信息控制器
 */
@Slf4j
@RestController
@RequestMapping("/monitoringPoint")
public class TMonitoringPointController {

    @Autowired
    private TMonitoringPointService tMonitoringPointService;

    /**
     * 根据ID查询监测点信息
     */
    @LogOperation
    @GetMapping("/{id}")
    public Result<TMonitoringPoint> selectById(@PathVariable Integer id) {
        if (id == null) {
            throw new ValidationException.ParameterNullException("监测点ID");
        }
        TMonitoringPoint monitoringPoint = tMonitoringPointService.selectByPrimaryKey(id);
        if (monitoringPoint != null) {
            return Result.success(monitoringPoint);
        } else {
            throw new ValidationException.DataNotFoundException("监测点", id.toString());
        }
    }

    /**
     * 新增监测点
     */
    @LogOperation
    @PostMapping("/add")
    public Result insertMonitoringPoint(@RequestBody TMonitoringPoint record) {
        if (record == null) {
            throw new ValidationException.ParameterNullException("监测点信息");
        }
        int result = tMonitoringPointService.insertMonitoringPoint(record);
        if (result > 0) {
            return Result.success();
        } else {
            throw new BusinessException.OperationFailedException("新增监测点");
        }
    }

    /**
     * 根据ID删除监测点
     */
    @LogOperation
    @DeleteMapping("/{id}")
    public Result deleteById(@PathVariable Integer id) {
        Map<String, Object> claims = ThreadLocalUtil.get();
        String role = (String) claims.get(RoleConstant.ROLE);
        if(!role.equals(RoleConstant.ADMIN)){
            throw new AuthenticationException.InsufficientPermissionException();
        }
        if (id == null) {
            throw new ValidationException.ParameterNullException("监测点ID");
        }
        int result = tMonitoringPointService.deleteByPrimaryKey(id);
        if (result > 0) {
            return Result.success();
        } else {
            throw new ValidationException.DataNotFoundException("监测点", id.toString());
        }
    }

    /**
     * 更新监测点信息
     */
    @LogOperation
    @PutMapping("/update")
    public Result updateBusinessFields(@RequestBody TMonitoringPoint record) {
        if (record == null) {
            throw new ValidationException.ParameterNullException("监测点信息");
        }
        if (record.getId() == null) {
            throw new ValidationException.ParameterNullException("监测点ID");
        }
        int result = tMonitoringPointService.updateBusinessFields(record);
        if (result > 0) {
            return Result.success();
        } else {
            throw new ValidationException.DataNotFoundException("监测点", record.getId().toString());
        }
    }

    /**
     * 分页查询监测点
     */
    @LogOperation
    @GetMapping("/selectPage")
    public Result<PageInfo<TMonitoringPoint>> selectPage(@RequestParam(defaultValue = "1") int pageNum,
                            @RequestParam(defaultValue = "10") int pageSize) {
        if (pageNum < 1) {
            throw new ValidationException.ParameterOutOfRangeException("页码", "大于0");
        }
        if (pageSize < 1 || pageSize > 100) {
            throw new ValidationException.ParameterOutOfRangeException("页面大小", "1-100");
        }
        PageInfo<TMonitoringPoint> pageInfo = tMonitoringPointService.selectPage(pageNum, pageSize);
        return Result.success(pageInfo);
    }

    /**
     * 根据条件查询监测点
     */
    @LogOperation
    @GetMapping("/selectByCondition")
    public Result<List<TMonitoringPoint>> selectByCondition(@RequestParam(required = false) Integer regionId,
                                   @RequestParam(required = false) String name,
                                   @RequestParam(required = false) String code,
                                   @RequestParam(required = false) String remark) {
        List<TMonitoringPoint> list = tMonitoringPointService.selectByCondition(regionId, name, code, remark);
        return Result.success(list);
    }

    /**
     * 分页条件查询监测点
     */
    @LogOperation
    @GetMapping("/selectPageByCondition")
    public Result<PageInfo<TMonitoringPoint>> selectPageByCondition(@RequestParam(required = false) Integer regionId,
                                       @RequestParam(required = false) String name,
                                       @RequestParam(required = false) String code,
                                       @RequestParam(required = false) String remark,
                                       @RequestParam(defaultValue = "1") int pageNum,
                                       @RequestParam(defaultValue = "10") int pageSize) {
        if (pageNum < 1) {
            throw new ValidationException.ParameterOutOfRangeException("页码", "大于0");
        }
        if (pageSize < 1 || pageSize > 100) {
            throw new ValidationException.ParameterOutOfRangeException("页面大小", "1-100");
        }
        PageInfo<TMonitoringPoint> pageInfo = tMonitoringPointService.selectPageByCondition(
                regionId, name, code, remark, pageNum, pageSize);
        return Result.success(pageInfo);
    }

    /**
     * 根据名称模糊查询监测点
     */
    @LogOperation
    @GetMapping("/selectByNameLike")
    public Result<List<TMonitoringPoint>> selectByNameLike(@RequestParam String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new ValidationException.ParameterNullException("监测点名称");
        }
        List<TMonitoringPoint> list = tMonitoringPointService.selectByNameLike(name);
        return Result.success(list);
    }

    /**
     * 根据地理位置模糊查询监测点
     */
    @LogOperation
    @GetMapping("/selectByLocationLike")
    public Result<List<TMonitoringPoint>> selectByLocationLike(@RequestParam String locationWkt) {
        if (locationWkt == null || locationWkt.trim().isEmpty()) {
            throw new ValidationException.ParameterNullException("地理位置信息");
        }
        List<TMonitoringPoint> list = tMonitoringPointService.selectByLocationLike(locationWkt);
        return Result.success(list);
    }

    /**
     * 根据地物ID查询监测点
     */
    @LogOperation
    @GetMapping("/selectByRegionId/{regionId}")
    public Result<List<TMonitoringPoint>> selectByRegionId(@PathVariable Integer regionId) {
        if (regionId == null) {
            throw new ValidationException.ParameterNullException("地物ID");
        }
        List<TMonitoringPoint> list = tMonitoringPointService.selectByRegionId(regionId);
        return Result.success(list);
    }
}
