package com.lysjk.controller;

import com.lysjk.anno.LogOperation;
import com.lysjk.dto.TWaterEnvironmentPageQueryDTO;
import com.lysjk.entity.TWaterEnvironment;
import com.lysjk.result.PageResult;
import com.lysjk.result.Result;
import com.lysjk.service.TWaterEnvironmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 水域环境信息控制层
 */
@RestController
@RequestMapping("/waterEnvironment")
@Slf4j
public class TWaterEnvironmentController {

    @Autowired
    private TWaterEnvironmentService waterEnvironmentService;

    /**
     * 新增水域环境信息
     * @param waterEnvironment 水域环境信息
     * @return 操作结果
     */
    @PostMapping
    @LogOperation(operationName = "新增水域环境信息")
    public Result save(@RequestBody TWaterEnvironment waterEnvironment) {
        log.info("新增水域环境信息: {}", waterEnvironment);
        waterEnvironmentService.save(waterEnvironment);
        return Result.success();
    }

    /**
     * 批量删除水域环境信息
     * 批量删除格式为?ids=1,2,3
     * @param ids ID列表
     * @return 操作结果
     */
    @DeleteMapping
    @LogOperation(operationName = "删除水域环境信息")
    public Result delete(@RequestParam List<Integer> ids) {
        log.info("批量删除水域环境信息: {}", ids);
        waterEnvironmentService.deleteBatch(ids);
        return Result.success();
    }

    /**
     * 更新水域环境信息
     * 必传id，然后是json格式
     * @param waterEnvironment 水域环境信息
     * @return 操作结果
     */
    @PutMapping
    @LogOperation(operationName = "更新水域环境信息")
    public Result update(@RequestBody TWaterEnvironment waterEnvironment) {
        log.info("更新水域环境信息: {}", waterEnvironment);
        waterEnvironmentService.update(waterEnvironment);
        return Result.success();
    }

    /**
     * 分页查询水域环境信息
     * 可以根据多个条件进行分页查询
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    @GetMapping("/page")
    @LogOperation(operationName = "分页查询水域环境信息")
    public Result<PageResult> selectPage(TWaterEnvironmentPageQueryDTO pageQueryDTO) {
        log.info("分页查询水域环境信息: {}", pageQueryDTO);
        PageResult pageResult = waterEnvironmentService.selectPage(pageQueryDTO);
        return Result.success(pageResult);
    }
}
