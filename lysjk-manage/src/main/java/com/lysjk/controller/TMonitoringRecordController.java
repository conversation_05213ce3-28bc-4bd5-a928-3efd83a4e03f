package com.lysjk.controller;

import com.lysjk.anno.LogOperation;
import com.lysjk.dto.TMonitoringRecordPageQueryDTO;
import com.lysjk.entity.TMonitoringRecord;
import com.lysjk.result.PageResult;
import com.lysjk.result.Result;
import com.lysjk.service.TMonitoringRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 测量记录控制层
 */
@RestController
@RequestMapping("/monitoringRecord")
@Slf4j
public class TMonitoringRecordController {

    @Autowired
    private TMonitoringRecordService monitoringRecordService;

    /**
     * 新增测量记录
     * @param monitoringRecord 测量记录信息
     * @return 操作结果
     */
    @PostMapping
    @LogOperation(operationName = "新增测量记录")
    public Result save(@RequestBody TMonitoringRecord monitoringRecord) {
        log.info("新增测量记录: {}", monitoringRecord);
        monitoringRecordService.save(monitoringRecord);
        return Result.success();
    }

    /**
     * 批量删除测量记录
     * 批量删除格式为?ids=1,2,3
     * @param ids ID列表
     * @return 操作结果
     */
    @DeleteMapping
    @LogOperation(operationName = "删除测量记录")
    public Result delete(@RequestParam List<Integer> ids) {
        log.info("批量删除测量记录: {}", ids);
        monitoringRecordService.deleteBatch(ids);
        return Result.success();
    }

    /**
     * 更新测量记录
     * 必传id,然后是json格式
     * @param monitoringRecord 测量记录信息
     * @return 操作结果
     */
    @PutMapping
    @LogOperation(operationName = "更新测量记录")
    public Result update(@RequestBody TMonitoringRecord monitoringRecord) {
        log.info("更新测量记录: {}", monitoringRecord);
        monitoringRecordService.update(monitoringRecord);
        return Result.success();
    }

    /**
     * 分页查询测量记录
     * 可以根据多个条件进行分页查询
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    @GetMapping("/page")
    @LogOperation(operationName = "分页查询测量记录")
    public Result<PageResult> selectPage(TMonitoringRecordPageQueryDTO pageQueryDTO) {
        log.info("分页查询测量记录: {}", pageQueryDTO);
        PageResult pageResult = monitoringRecordService.selectPage(pageQueryDTO);
        return Result.success(pageResult);
    }
}
