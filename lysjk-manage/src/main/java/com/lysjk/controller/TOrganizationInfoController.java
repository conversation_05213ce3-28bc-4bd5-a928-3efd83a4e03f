package com.lysjk.controller;

import com.lysjk.anno.LogOperation;
import com.lysjk.dto.TOrganizationInfoPageQueryDTO;
import com.lysjk.entity.TOrganizationInfo;
import com.lysjk.result.PageResult;
import com.lysjk.result.Result;
import com.lysjk.service.TOrganizationInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 单位信息控制层
 */
@RestController
@RequestMapping("/organizationInfo")
@Slf4j
public class TOrganizationInfoController {

    @Autowired
    private TOrganizationInfoService organizationInfoService;

    /**
     * 新增单位
     * @param organizationInfo
     * @return
     */
    @PostMapping
    @LogOperation(operationName = "新增单位")
    public Result save(@RequestBody TOrganizationInfo organizationInfo){
        log.info("新增的单位信息: {}", organizationInfo);
        organizationInfoService.save(organizationInfo);
        return Result.success();
    }

    /**
     * 删除单位,就先不做判断了,因为前端渲染出来都没必要
     * 批量删除格式为?ids=1,2,3
     * @param ids
     * @return
     */
    @LogOperation(operationName = "删除单位")
    @DeleteMapping
    public Result delete(@RequestParam List<Long> ids){
        log.info("批量删除单位: {}", ids);
        organizationInfoService.deleteBath(ids);
        return Result.success();
    }

    /**
     * 更新单位信息
     * 必传id,然后是json格式
     * @param organizationInfo
     * @return
     */
    @LogOperation(operationName = "更新单位")
    @PutMapping
    public Result update(@RequestBody TOrganizationInfo organizationInfo){
        log.info("更新单位信息: {}", organizationInfo);
        organizationInfoService.update(organizationInfo);
        return Result.success();
    }

    /**
     * 可以根据名称分页查询
     * 然后是搜索框类型,所以用Param即可
     * 这里进行了二次封装
     * @param organizationInfoPageQueryDTO
     * @return
     */
    @LogOperation(operationName = "分页查询单位")
    @GetMapping("/page")
    public Result<PageResult> selectPage(TOrganizationInfoPageQueryDTO organizationInfoPageQueryDTO){
        log.info("分页查询单位信息: {}", organizationInfoPageQueryDTO);
        PageResult pageResult = organizationInfoService.selectPage(organizationInfoPageQueryDTO);
        return Result.success(pageResult);
    }
}
