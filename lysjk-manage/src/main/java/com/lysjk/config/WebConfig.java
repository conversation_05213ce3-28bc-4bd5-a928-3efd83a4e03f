package com.lysjk.config;

import com.lysjk.interceptors.TokenInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 配置类,注册拦截器interceptor
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {
    @Value("${upload.path}")
    private String uploadPath;

    //拦截器对象
    @Autowired
    private TokenInterceptor tokenInterceptor; // 注入容器中就不需要new对象了

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //注册自定义拦截器对象
        registry.addInterceptor(tokenInterceptor)
                .addPathPatterns("/**") //设置拦截器拦截的请求路径（ /** 表示拦截所有请求）
                .excludePathPatterns("/login"); //设置不拦截的请求路径

    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 映射上传目录到URL路径（如http://localhost:8080/upload/**）
        registry.addResourceHandler("/upload/**")
                .addResourceLocations("file:" + uploadPath); // "file:"前缀表示文件系统路径
    }
}
