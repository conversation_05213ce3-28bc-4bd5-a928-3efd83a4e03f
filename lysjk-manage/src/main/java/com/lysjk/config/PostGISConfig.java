package com.lysjk.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.Statement;

/**
 * PostGIS配置类
 * 确保PostGIS扩展正确加载
 */
@Slf4j
@Configuration
public class PostGISConfig implements CommandLineRunner {

    @Autowired
    private DataSource dataSource;

    @Override
    public void run(String... args) throws Exception {
        initPostGIS();
    }

    public void initPostGIS() {
        try (Connection connection = dataSource.getConnection()) {
            // 检查PostGIS扩展是否已安装
            try (Statement statement = connection.createStatement()) {
                // 尝试创建PostGIS扩展（如果不存在）
                statement.execute("CREATE EXTENSION IF NOT EXISTS postgis");
                log.info("PostGIS扩展检查完成");

                // 验证PostGIS版本
                var resultSet = statement.executeQuery("SELECT PostGIS_Version()");
                if (resultSet.next()) {
                    String version = resultSet.getString(1);
                    log.info("PostGIS版本: {}", version);
                } else {
                    log.warn("无法获取PostGIS版本");
                }

            } catch (Exception e) {
                log.warn("PostGIS扩展检查失败，可能需要手动安装PostGIS扩展: {}", e.getMessage());
            }

        } catch (Exception e) {
            log.error("PostGIS配置初始化失败", e);
            throw new RuntimeException("PostGIS初始化失败，请检查数据库配置", e);
        }
    }
}

//package com.lysjk.config;
//
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.CommandLineRunner;
//import org.springframework.context.annotation.Configuration;
//
//import javax.sql.DataSource;
//import java.sql.Connection;
//import java.sql.Statement;
//
///**
// * PostGIS配置类
// * 确保PostGIS扩展正确加载
// */
//@Slf4j
//@Configuration
//public class PostGISConfig implements CommandLineRunner {
//
//    @Autowired
//    private DataSource dataSource;
//
//    @Override
//    public void run(String... args) throws Exception {
//        initPostGIS();
//    }
//
//    public void initPostGIS() {
//        try (Connection connection = dataSource.getConnection()) {
//            // 检查PostGIS扩展是否已安装
//            try (Statement statement = connection.createStatement()) {
//                // 尝试创建PostGIS扩展（如果不存在）
//                statement.execute("CREATE EXTENSION IF NOT EXISTS postgis");
//                log.info("PostGIS扩展检查完成");
//
//                // 验证PostGIS版本
//                var resultSet = statement.executeQuery("SELECT PostGIS_Version()");
//                if (resultSet.next()) {
//                    String version = resultSet.getString(1);
//                    log.info("PostGIS版本: {}", version);
//                }
//
//            } catch (Exception e) {
//                log.warn("PostGIS扩展检查失败，可能需要手动安装PostGIS扩展: {}", e.getMessage());
//            }
//
//        } catch (Exception e) {
//            log.error("PostGIS配置初始化失败", e);
//        }
//    }
//}
