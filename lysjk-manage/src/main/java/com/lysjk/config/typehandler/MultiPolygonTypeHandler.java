package com.lysjk.config.typehandler;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import org.locationtech.jts.geom.MultiPolygon;
import org.locationtech.jts.geom.Polygon;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.WKBReader;
import org.locationtech.jts.io.WKTReader;
import org.locationtech.jts.io.WKTWriter;
import org.postgresql.util.PGobject;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * PostGIS MultiPolygon类型处理器
 * 处理Java MultiPolygon对象与PostgreSQL geometry类型之间的转换
 */
@Slf4j
@MappedTypes(MultiPolygon.class)
public class MultiPolygonTypeHandler extends BaseTypeHandler<MultiPolygon> {

    private static final WKTReader WKT_READER = new WKTReader();
    private static final WKTWriter WKT_WRITER = new WKTWriter();
    private static final WKBReader WKB_READER = new WKBReader();

    private static final int SRID = 4326; // 可配置的SRID

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, MultiPolygon parameter, JdbcType jdbcType) throws SQLException {
        try {
            // 将MultiPolygon对象转换为PostGIS可识别的格式
            PGobject pGobject = new PGobject();
            pGobject.setType("geometry");

            if (parameter != null) {
                // 使用WKT格式，并指定SRID
                String wkt = WKT_WRITER.write(parameter);
                pGobject.setValue("SRID=" + SRID + ";" + wkt);
            } else {
                pGobject.setValue(null);
            }

            ps.setObject(i, pGobject);

            if (log.isDebugEnabled()) {
                log.debug("设置MultiPolygon参数: {}", WKT_WRITER.write(parameter));
            }

        } catch (Exception e) {
            log.error("设置MultiPolygon参数失败：{}", parameter, e);
            throw new SQLException("设置MultiPolygon参数失败，数据：" + parameter, e);
        }
    }

    @Override
    public MultiPolygon getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return getMultiPolygon(rs.getObject(columnName));
    }

    @Override
    public MultiPolygon getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return getMultiPolygon(rs.getObject(columnIndex));
    }

    @Override
    public MultiPolygon getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return getMultiPolygon(cs.getObject(columnIndex));
    }

    /**
     * 从数据库对象转换为MultiPolygon
     */
    private MultiPolygon getMultiPolygon(Object object) throws SQLException {
        if (object == null) {
            return null;
        }

        try {
            org.locationtech.jts.geom.Geometry geometry = parseGeometry(object);

            if (geometry instanceof MultiPolygon) {
                return (MultiPolygon) geometry;
            } else if (geometry instanceof Polygon) {
                return createMultiPolygonFromPolygon((Polygon) geometry);
            } else {
                throw new SQLException("几何对象不是MultiPolygon或Polygon类型: " + geometry.getGeometryType());
            }
        } catch (Exception e) {
            log.error("转换MultiPolygon失败: {}", object, e);
            throw new SQLException("无法转换MultiPolygon数据", e);
        }
    }

    private MultiPolygon parseGeometry(Object object) throws SQLException {
        try {
            org.locationtech.jts.geom.Geometry geometry = null;

            if (object instanceof PGobject) {
                String value = ((PGobject) object).getValue();
                geometry = parseWKT(value);
            } else if (object instanceof String) {
                geometry = parseWKT((String) object);
            } else if (object instanceof byte[]) {
                geometry = WKB_READER.read((byte[]) object);
            } else {
                log.warn("未知的MultiPolygon数据类型: {}", object.getClass().getName());
                return null;
            }

            return (MultiPolygon) geometry;
        } catch (Exception e) {
            log.error("解析几何对象失败: {}", object, e);
            throw new SQLException("无法解析几何数据", e);
        }
    }

    private MultiPolygon parseWKT(String wkt) throws ParseException {
        if (wkt != null && wkt.startsWith("SRID=")) {
            int semicolonIndex = wkt.indexOf(';');
            wkt = wkt.substring(semicolonIndex + 1);
        }
        return (MultiPolygon) WKT_READER.read(wkt);
    }

    private MultiPolygon createMultiPolygonFromPolygon(Polygon polygon) {
        polygon.setSRID(SRID);
        return polygon.getFactory().createMultiPolygon(new Polygon[]{polygon});
    }
}


//package com.lysjk.config.typehandler;
//
//import lombok.extern.slf4j.Slf4j;
//import org.apache.ibatis.type.BaseTypeHandler;
//import org.apache.ibatis.type.JdbcType;
//import org.apache.ibatis.type.MappedTypes;
//import org.locationtech.jts.geom.MultiPolygon;
//import org.locationtech.jts.geom.Polygon;
//import org.locationtech.jts.io.ParseException;
//import org.locationtech.jts.io.WKBReader;
//import org.locationtech.jts.io.WKBWriter;
//import org.locationtech.jts.io.WKTReader;
//import org.locationtech.jts.io.WKTWriter;
//import org.postgresql.util.PGobject;
//
//import java.sql.CallableStatement;
//import java.sql.PreparedStatement;
//import java.sql.ResultSet;
//import java.sql.SQLException;
//
///**
// * PostGIS MultiPolygon类型处理器
// * 处理Java MultiPolygon对象与PostgreSQL geometry类型之间的转换
// */
//@Slf4j
//@MappedTypes(MultiPolygon.class)
//public class MultiPolygonTypeHandler extends BaseTypeHandler<MultiPolygon> {
//
//    private static final WKTReader WKT_READER = new WKTReader();
//    private static final WKTWriter WKT_WRITER = new WKTWriter();
//    private static final WKBReader WKB_READER = new WKBReader();
//    private static final WKBWriter WKB_WRITER = new WKBWriter();
//
//    @Override
//    public void setNonNullParameter(PreparedStatement ps, int i, MultiPolygon parameter, JdbcType jdbcType) throws SQLException {
//        try {
//            // 将MultiPolygon对象转换为PostGIS可识别的格式
//            PGobject pGobject = new PGobject();
//            pGobject.setType("geometry");
//
//            if (parameter != null) {
//                // 使用WKT格式，并指定SRID
//                String wkt = WKT_WRITER.write(parameter);
//                pGobject.setValue("SRID=4326;" + wkt);
//            } else {
//                pGobject.setValue(null);
//            }
//
//            ps.setObject(i, pGobject);
//            log.debug("设置MultiPolygon参数: {}", parameter != null ? WKT_WRITER.write(parameter) : "null");
//
//        } catch (Exception e) {
//            log.error("设置MultiPolygon参数失败", e);
//            throw new SQLException("无法设置MultiPolygon参数", e);
//        }
//    }
//
//    @Override
//    public MultiPolygon getNullableResult(ResultSet rs, String columnName) throws SQLException {
//        return getMultiPolygon(rs.getObject(columnName));
//    }
//
//    @Override
//    public MultiPolygon getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
//        return getMultiPolygon(rs.getObject(columnIndex));
//    }
//
//    @Override
//    public MultiPolygon getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
//        return getMultiPolygon(cs.getObject(columnIndex));
//    }
//
//    /**
//     * 从数据库对象转换为MultiPolygon
//     */
//    private MultiPolygon getMultiPolygon(Object object) throws SQLException {
//        if (object == null) {
//            return null;
//        }
//
//        try {
//            if (object instanceof PGobject) {
//                PGobject pGobject = (PGobject) object;
//                String value = pGobject.getValue();
//
//                if (value == null || value.trim().isEmpty()) {
//                    return null;
//                }
//
//                // 处理带SRID的WKT格式
//                String wkt = value;
//                if (value.startsWith("SRID=")) {
//                    int semicolonIndex = value.indexOf(';');
//                    if (semicolonIndex > 0) {
//                        wkt = value.substring(semicolonIndex + 1);
//                    }
//                }
//
//                // 解析WKT为MultiPolygon对象
//                org.locationtech.jts.geom.Geometry geometry = WKT_READER.read(wkt);
//
//                if (geometry instanceof MultiPolygon) {
//                    MultiPolygon multiPolygon = (MultiPolygon) geometry;
//                    // 设置SRID
//                    multiPolygon.setSRID(4326);
//                    log.debug("解析MultiPolygon成功: {}", WKT_WRITER.write(multiPolygon));
//                    return multiPolygon;
//                } else if (geometry instanceof Polygon) {
//                    // 如果是单个Polygon，转换为MultiPolygon
//                    Polygon polygon = (Polygon) geometry;
//                    polygon.setSRID(4326);
//                    Polygon[] polygons = new Polygon[]{polygon};
//                    MultiPolygon multiPolygon = geometry.getFactory().createMultiPolygon(polygons);
//                    multiPolygon.setSRID(4326);
//                    log.debug("将Polygon转换为MultiPolygon成功: {}", WKT_WRITER.write(multiPolygon));
//                    return multiPolygon;
//                } else {
//                    throw new SQLException("几何对象不是MultiPolygon或Polygon类型: " + geometry.getGeometryType());
//                }
//
//            } else if (object instanceof String) {
//                // 直接是WKT字符串
//                String wkt = (String) object;
//                org.locationtech.jts.geom.Geometry geometry = WKT_READER.read(wkt);
//
//                if (geometry instanceof MultiPolygon) {
//                    MultiPolygon multiPolygon = (MultiPolygon) geometry;
//                    multiPolygon.setSRID(4326);
//                    return multiPolygon;
//                } else if (geometry instanceof Polygon) {
//                    Polygon polygon = (Polygon) geometry;
//                    polygon.setSRID(4326);
//                    Polygon[] polygons = new Polygon[]{polygon};
//                    MultiPolygon multiPolygon = geometry.getFactory().createMultiPolygon(polygons);
//                    multiPolygon.setSRID(4326);
//                    return multiPolygon;
//                } else {
//                    throw new SQLException("几何对象不是MultiPolygon或Polygon类型: " + geometry.getGeometryType());
//                }
//
//            } else if (object instanceof byte[]) {
//                // WKB二进制格式
//                byte[] wkb = (byte[]) object;
//                org.locationtech.jts.geom.Geometry geometry = WKB_READER.read(wkb);
//
//                if (geometry instanceof MultiPolygon) {
//                    MultiPolygon multiPolygon = (MultiPolygon) geometry;
//                    multiPolygon.setSRID(4326);
//                    return multiPolygon;
//                } else if (geometry instanceof Polygon) {
//                    Polygon polygon = (Polygon) geometry;
//                    polygon.setSRID(4326);
//                    Polygon[] polygons = new Polygon[]{polygon};
//                    MultiPolygon multiPolygon = geometry.getFactory().createMultiPolygon(polygons);
//                    multiPolygon.setSRID(4326);
//                    return multiPolygon;
//                } else {
//                    throw new SQLException("几何对象不是MultiPolygon或Polygon类型: " + geometry.getGeometryType());
//                }
//
//            } else {
//                log.warn("未知的MultiPolygon数据类型: {}", object.getClass().getName());
//                return null;
//            }
//
//        } catch (ParseException e) {
//            log.error("解析MultiPolygon失败: {}", object, e);
//            throw new SQLException("无法解析MultiPolygon数据", e);
//        } catch (Exception e) {
//            log.error("转换MultiPolygon失败: {}", object, e);
//            throw new SQLException("无法转换MultiPolygon数据", e);
//        }
//    }
//}