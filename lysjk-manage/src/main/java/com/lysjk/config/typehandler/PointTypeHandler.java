package com.lysjk.config.typehandler;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.io.WKBReader;
import org.locationtech.jts.io.WKTReader;
import org.locationtech.jts.io.WKTWriter;
import org.postgresql.util.PGobject;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * PostGIS Point类型处理器
 * 处理Java Point对象与PostgreSQL geometry类型之间的转换
 */
@Slf4j
@MappedTypes(Point.class)
public class PointTypeHandler extends BaseTypeHandler<Point> {

    private static final WKTReader WKT_READER = new WKTReader();
    private static final WKTWriter WKT_WRITER = new WKTWriter();
    private static final WKBReader WKB_READER = new WKBReader();

    private static final int SRID = 4326; // 可配置的SRID

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Point parameter, JdbcType jdbcType) throws SQLException {
        try {
            // 将Point对象转换为PostGIS可识别的格式
            PGobject pGobject = new PGobject();
            pGobject.setType("geometry");

            if (parameter != null) {
                // 使用WKT格式，并指定SRID
                String wkt = WKT_WRITER.write(parameter);
                pGobject.setValue("SRID=" + SRID + ";" + wkt);
            } else {
                pGobject.setValue(null);
            }

            ps.setObject(i, pGobject);

            if (log.isDebugEnabled()) {
                log.debug("设置Point参数: {}", WKT_WRITER.write(parameter));
            }

        } catch (Exception e) {
            log.error("设置Point参数失败：{}", parameter, e);
            throw new SQLException("设置Point参数失败，数据：" + parameter, e);
        }
    }

    @Override
    public Point getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return getPoint(rs.getObject(columnName));
    }

    @Override
    public Point getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return getPoint(rs.getObject(columnIndex));
    }

    @Override
    public Point getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return getPoint(cs.getObject(columnIndex));
    }

    /**
     * 从数据库对象转换为Point
     */
    private Point getPoint(Object object) throws SQLException {
        if (object == null) {
            return null;
        }

        try {
            org.locationtech.jts.geom.Geometry geometry = parseGeometry(object);

            if (geometry instanceof Point) {
                return (Point) geometry;
            } else {
                throw new SQLException("几何对象不是Point类型: " + geometry.getGeometryType());
            }

        } catch (Exception e) {
            log.error("转换Point失败: {}", object, e);
            throw new SQLException("无法转换Point数据", e);
        }
    }

    private org.locationtech.jts.geom.Geometry parseGeometry(Object object) throws SQLException {
        try {
            if (object instanceof PGobject) {
                String value = ((PGobject) object).getValue();
                return parseWKT(value);
            } else if (object instanceof String) {
                return parseWKT((String) object);
            } else if (object instanceof byte[]) {
                return WKB_READER.read((byte[]) object);
            } else {
                log.warn("未知的Point数据类型: {}", object.getClass().getName());
                return null;
            }
        } catch (Exception e) {
            log.error("解析几何对象失败: {}", object, e);
            throw new SQLException("无法解析几何数据", e);
        }
    }

    private Point parseWKT(String wkt) throws ParseException {
        if (wkt != null && wkt.startsWith("SRID=")) {
            int semicolonIndex = wkt.indexOf(';');
            wkt = wkt.substring(semicolonIndex + 1);
        }
        return (Point) WKT_READER.read(wkt);
    }
}

//package com.lysjk.config.typehandler;
//
//import lombok.extern.slf4j.Slf4j;
//import org.apache.ibatis.type.BaseTypeHandler;
//import org.apache.ibatis.type.JdbcType;
//import org.apache.ibatis.type.MappedTypes;
//import org.locationtech.jts.geom.Point;
//import org.locationtech.jts.io.ParseException;
//import org.locationtech.jts.io.WKBReader;
//import org.locationtech.jts.io.WKBWriter;
//import org.locationtech.jts.io.WKTReader;
//import org.locationtech.jts.io.WKTWriter;
//import org.postgresql.util.PGobject;
//
//import java.sql.CallableStatement;
//import java.sql.PreparedStatement;
//import java.sql.ResultSet;
//import java.sql.SQLException;
//
///**
// * PostGIS Point类型处理器
// * 处理Java Point对象与PostgreSQL geometry类型之间的转换
// */
//@Slf4j
//@MappedTypes(Point.class)
//public class PointTypeHandler extends BaseTypeHandler<Point> {
//
//    private static final WKTReader WKT_READER = new WKTReader();
//    private static final WKTWriter WKT_WRITER = new WKTWriter();
//    private static final WKBReader WKB_READER = new WKBReader();
//    private static final WKBWriter WKB_WRITER = new WKBWriter();
//
//    @Override
//    public void setNonNullParameter(PreparedStatement ps, int i, Point parameter, JdbcType jdbcType) throws SQLException {
//        try {
//            // 将Point对象转换为PostGIS可识别的格式
//            PGobject pGobject = new PGobject();
//            pGobject.setType("geometry");
//
//            if (parameter != null) {
//                // 使用WKT格式，并指定SRID
//                String wkt = WKT_WRITER.write(parameter);
//                pGobject.setValue("SRID=4326;" + wkt);
//            } else {
//                pGobject.setValue(null);
//            }
//
//            ps.setObject(i, pGobject);
//            log.debug("设置Point参数: {}", parameter != null ? WKT_WRITER.write(parameter) : "null");
//
//        } catch (Exception e) {
//            log.error("设置Point参数失败", e);
//            throw new SQLException("无法设置Point参数", e);
//        }
//    }
//
//    @Override
//    public Point getNullableResult(ResultSet rs, String columnName) throws SQLException {
//        return getPoint(rs.getObject(columnName));
//    }
//
//    @Override
//    public Point getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
//        return getPoint(rs.getObject(columnIndex));
//    }
//
//    @Override
//    public Point getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
//        return getPoint(cs.getObject(columnIndex));
//    }
//
//    /**
//     * 从数据库对象转换为Point
//     */
//    private Point getPoint(Object object) throws SQLException {
//        if (object == null) {
//            return null;
//        }
//
//        try {
//            if (object instanceof PGobject) {
//                PGobject pGobject = (PGobject) object;
//                String value = pGobject.getValue();
//
//                if (value == null || value.trim().isEmpty()) {
//                    return null;
//                }
//
//                // 处理带SRID的WKT格式
//                String wkt = value;
//                if (value.startsWith("SRID=")) {
//                    int semicolonIndex = value.indexOf(';');
//                    if (semicolonIndex > 0) {
//                        wkt = value.substring(semicolonIndex + 1);
//                    }
//                }
//
//                // 解析WKT为Point对象
//                org.locationtech.jts.geom.Geometry geometry = WKT_READER.read(wkt);
//                if (geometry instanceof Point) {
//                    Point point = (Point) geometry;
//                    // 设置SRID
//                    point.setSRID(4326);
//                    log.debug("解析Point成功: {}", WKT_WRITER.write(point));
//                    return point;
//                } else {
//                    throw new SQLException("几何对象不是Point类型: " + geometry.getGeometryType());
//                }
//
//            } else if (object instanceof String) {
//                // 直接是WKT字符串
//                String wkt = (String) object;
//                org.locationtech.jts.geom.Geometry geometry = WKT_READER.read(wkt);
//                if (geometry instanceof Point) {
//                    Point point = (Point) geometry;
//                    point.setSRID(4326);
//                    return point;
//                } else {
//                    throw new SQLException("几何对象不是Point类型: " + geometry.getGeometryType());
//                }
//
//            } else if (object instanceof byte[]) {
//                // WKB二进制格式
//                byte[] wkb = (byte[]) object;
//                org.locationtech.jts.geom.Geometry geometry = WKB_READER.read(wkb);
//                if (geometry instanceof Point) {
//                    Point point = (Point) geometry;
//                    point.setSRID(4326);
//                    return point;
//                } else {
//                    throw new SQLException("几何对象不是Point类型: " + geometry.getGeometryType());
//                }
//
//            } else {
//                log.warn("未知的Point数据类型: {}", object.getClass().getName());
//                return null;
//            }
//
//        } catch (ParseException e) {
//            log.error("解析Point失败: {}", object, e);
//            throw new SQLException("无法解析Point数据", e);
//        } catch (Exception e) {
//            log.error("转换Point失败: {}", object, e);
//            throw new SQLException("无法转换Point数据", e);
//        }
//    }
//}