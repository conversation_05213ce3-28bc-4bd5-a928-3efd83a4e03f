package com.lysjk.config.typehandler;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import org.postgresql.util.PGobject;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;

/**
 * JSON Map类型处理器
 * 处理Java Map<String, Double>对象与PostgreSQL json/jsonb类型之间的转换
 */
@Slf4j
@MappedTypes(Map.class)
public class JsonMapTypeHandler extends BaseTypeHandler<Map<String, Double>> {

    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper();
    private static final TypeReference<Map<String, Double>> TYPE_REFERENCE = new TypeReference<Map<String, Double>>() {};

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Map<String, Double> parameter, JdbcType jdbcType) throws SQLException {
        try {
            // 将Map对象转换为PostgreSQL JSON格式
            PGobject pGobject = new PGobject();
            pGobject.setType("json");

            if (parameter != null && !parameter.isEmpty()) {
                String jsonString = OBJECT_MAPPER.writeValueAsString(parameter);
                pGobject.setValue(jsonString);
                
                if (log.isDebugEnabled()) {
                    log.debug("设置JSON Map参数: {}", jsonString);
                }
            } else {
                pGobject.setValue(null);
            }

            ps.setObject(i, pGobject);

        } catch (Exception e) {
            log.error("设置JSON Map参数失败：{}", parameter, e);
            throw new SQLException("设置JSON Map参数失败，数据：" + parameter, e);
        }
    }

    @Override
    public Map<String, Double> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return parseJsonMap(rs.getObject(columnName));
    }

    @Override
    public Map<String, Double> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return parseJsonMap(rs.getObject(columnIndex));
    }

    @Override
    public Map<String, Double> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return parseJsonMap(cs.getObject(columnIndex));
    }

    /**
     * 解析JSON数据为Map对象
     */
    private Map<String, Double> parseJsonMap(Object object) throws SQLException {
        if (object == null) {
            return null;
        }

        try {
            String jsonString;
            if (object instanceof PGobject) {
                jsonString = ((PGobject) object).getValue();
            } else if (object instanceof String) {
                jsonString = (String) object;
            } else {
                log.warn("未知的JSON数据类型: {}", object.getClass().getName());
                return null;
            }

            if (jsonString == null || jsonString.trim().isEmpty()) {
                return null;
            }

            Map<String, Double> result = OBJECT_MAPPER.readValue(jsonString, TYPE_REFERENCE);
            
            if (log.isDebugEnabled()) {
                log.debug("解析JSON Map成功，数据量: {}", result != null ? result.size() : 0);
            }
            
            return result;

        } catch (Exception e) {
            log.error("解析JSON Map失败: {}", object, e);
            throw new SQLException("无法解析JSON Map数据", e);
        }
    }
}
