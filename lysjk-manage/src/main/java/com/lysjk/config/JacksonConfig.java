package com.lysjk.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.lysjk.config.serializer.MultiPolygonDeserializer;
import com.lysjk.config.serializer.MultiPolygonSerializer;
import com.lysjk.config.serializer.PointDeserializer;
import com.lysjk.config.serializer.PointSerializer;
import org.locationtech.jts.geom.MultiPolygon;
import org.locationtech.jts.geom.Point;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * Jackson配置类
 * 配置PostGIS几何对象的JSON序列化和反序列化
 */
@Configuration
public class JacksonConfig {

    @Bean
    @Primary
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();

        // 注册Java 8时间模块
        objectMapper.registerModule(new JavaTimeModule());

        // 创建PostGIS几何对象序列化模块
        SimpleModule geometryModule = new SimpleModule("PostGISGeometryModule");
        geometryModule.addSerializer(Point.class, new PointSerializer());
        geometryModule.addDeserializer(Point.class, new PointDeserializer());
        geometryModule.addSerializer(MultiPolygon.class, new MultiPolygonSerializer());
        geometryModule.addDeserializer(MultiPolygon.class, new MultiPolygonDeserializer());
        objectMapper.registerModule(geometryModule);

        // 禁用将日期写为时间戳
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);

        // 忽略未知属性
        objectMapper.configure(com.fasterxml.jackson.databind.DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        // 防止循环引用导致的深度嵌套
        objectMapper.configure(SerializationFeature.FAIL_ON_SELF_REFERENCES, false);

        return objectMapper;
    }
}
