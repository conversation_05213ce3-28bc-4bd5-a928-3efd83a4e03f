package com.lysjk.mapper;

import com.github.pagehelper.Page;
import com.lysjk.anno.AutoFill;
import com.lysjk.dto.TWaterSpectrumPageQueryDTO;
import com.lysjk.entity.TWaterSpectrum;
import com.lysjk.enums.Operation;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 波谱信息Mapper接口
 */
@Mapper
public interface TWaterSpectrumMapper {
    
    /**
     * 新增波谱信息
     * @param waterSpectrum 波谱信息
     * @return 影响行数
     */
    @AutoFill(Operation.INSERT)
    int insert(TWaterSpectrum waterSpectrum);
    
    /**
     * 批量删除波谱信息
     * @param ids ID列表
     * @return 影响行数
     */
    int deleteByIds(List<Integer> ids);
    
    /**
     * 更新波谱信息
     * @param waterSpectrum 波谱信息
     * @return 影响行数
     */
    @AutoFill(Operation.UPDATE)
    int update(TWaterSpectrum waterSpectrum);
    
    /**
     * 分页查询波谱信息
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    Page<TWaterSpectrum> pageQuery(TWaterSpectrumPageQueryDTO pageQueryDTO);
}
