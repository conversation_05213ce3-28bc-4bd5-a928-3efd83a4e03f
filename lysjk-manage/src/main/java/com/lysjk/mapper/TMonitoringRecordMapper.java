package com.lysjk.mapper;

import com.github.pagehelper.Page;
import com.lysjk.anno.AutoFill;
import com.lysjk.dto.TMonitoringRecordPageQueryDTO;
import com.lysjk.entity.TMonitoringRecord;
import com.lysjk.enums.Operation;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 测量记录Mapper接口
 */
@Mapper
public interface TMonitoringRecordMapper {
    
    /**
     * 新增测量记录
     * @param monitoringRecord 测量记录信息
     * @return 影响行数
     */
    @AutoFill(Operation.INSERT)
    int insert(TMonitoringRecord monitoringRecord);
    
    /**
     * 批量删除测量记录
     * @param ids ID列表
     * @return 影响行数
     */
    int deleteByIds(List<Integer> ids);
    
    /**
     * 更新测量记录
     * @param monitoringRecord 测量记录信息
     * @return 影响行数
     */
    @AutoFill(Operation.UPDATE)
    int update(TMonitoringRecord monitoringRecord);
    
    /**
     * 分页查询测量记录
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    Page<TMonitoringRecord> pageQuery(TMonitoringRecordPageQueryDTO pageQueryDTO);
}
