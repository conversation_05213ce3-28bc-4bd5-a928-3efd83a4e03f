package com.lysjk.mapper;

import com.lysjk.anno.AutoFill;
import com.lysjk.entity.TMonitoringPoint;
import com.lysjk.enums.Operation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 监测点信息表数据访问层
 */
@Mapper
public interface TMonitoringPointMapper {

    /**
     * 根据主键删除监测点
     * @param id 监测点ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 插入监测点信息（业务字段）
     * @param record 监测点信息
     * @return 影响行数
     */
    @AutoFill(Operation.INSERT)
    int insertBusinessFields(TMonitoringPoint record);

    /**
     * 根据主键查询监测点
     * @param id 监测点ID
     * @return 监测点信息
     */
    TMonitoringPoint selectByPrimaryKey(Integer id);

    /**
     * 更新监测点业务字段
     * @param record 监测点信息
     * @return 影响行数
     */
    int updateBusinessFields(TMonitoringPoint record);

    /**
     * 查询所有监测点
     * @return 监测点列表
     */
    List<TMonitoringPoint> selectAll();

    /**
     * 根据条件查询监测点
     * @param regionId 所属地物ID
     * @param name 监测点名称
     * @param code 监测点编号
     * @param remark 备注
     * @return 监测点列表
     */
    List<TMonitoringPoint> selectByCondition(@Param("regionId") Integer regionId,
                                           @Param("name") String name,
                                           @Param("code") String code,
                                           @Param("remark") String remark);

    /**
     * 根据名称模糊查询监测点
     * @param name 监测点名称关键字
     * @return 监测点列表
     */
    List<TMonitoringPoint> selectByNameLike(@Param("name") String name);

    /**
     * 根据地理位置模糊查询监测点
     * @param locationWkt 地理位置WKT字符串关键字
     * @return 监测点列表
     */
    List<TMonitoringPoint> selectByLocationLike(@Param("locationWkt") String locationWkt);

    /**
     * 根据地物ID查询监测点
     * @param regionId 地物ID
     * @return 监测点列表
     */
    List<TMonitoringPoint> selectByRegionId(@Param("regionId") Integer regionId);
}