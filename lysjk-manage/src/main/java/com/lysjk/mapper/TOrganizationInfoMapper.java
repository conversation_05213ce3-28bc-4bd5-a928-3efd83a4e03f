package com.lysjk.mapper;

import com.github.pagehelper.Page;
import com.lysjk.anno.AutoFill;
import com.lysjk.dto.TOrganizationInfoPageQueryDTO;
import com.lysjk.entity.TOrganizationInfo;
import com.lysjk.enums.Operation;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Update;

import java.util.List;

@Mapper
public interface TOrganizationInfoMapper {
    /**
     * 新增单位信息
     * remark非必须,其余必须
     * @param organizationInfo
     */
    @AutoFill(Operation.INSERT)
    @Insert("insert into t_organization_info (name, create_dt, create_by, update_dt, update_by, remark) " +
            "values (#{name}, #{createDt}, #{createBy}, #{updateDt}, #{updateBy}, #{remark})")
    void insert(TOrganizationInfo organizationInfo);

    /**
     * 批量删除单位信息
     * @param ids
     */
    void deleteByIds(List<Long> ids);

    /**
     * 更新单位信息
     * @param organizationInfo
     */
    @AutoFill(Operation.UPDATE)
    @Update("update t_organization_info set name = #{name}, remark = #{remark}, update_dt = #{updateDt}, update_by = #{updateBy} where id = #{id}")
    void update(TOrganizationInfo organizationInfo);

    /**
     * 分页查询都用到了动态sql,所以直接写在映射文件中
     * @param organizationInfoPageQueryDTO
     * @return
     */
    Page<TOrganizationInfo> pageQuery(TOrganizationInfoPageQueryDTO organizationInfoPageQueryDTO);
}
