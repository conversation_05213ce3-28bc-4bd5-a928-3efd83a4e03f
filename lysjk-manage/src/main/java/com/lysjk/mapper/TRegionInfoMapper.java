package com.lysjk.mapper;

import com.lysjk.anno.AutoFill;
import com.lysjk.dto.TRegionInfoWithMonitoringPointsDTO;
import com.lysjk.entity.TRegionInfo;
import com.lysjk.enums.Operation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.locationtech.jts.geom.MultiPolygon;
import org.locationtech.jts.geom.Point;

import java.util.List;

/**
 * 地物信息表Mapper接口
 */
@Mapper
public interface TRegionInfoMapper {

    // 基础CRUD操作
    int deleteByPrimaryKey(Integer id);

    int insert(TRegionInfo record);

    int insertSelective(TRegionInfo record);

    TRegionInfo selectByPrimaryKey(Integer id);



    int updateByPrimaryKey(TRegionInfo record);

    // 扩展的业务方法

    /**
     * 根据行政区划条件查询地物信息
     * @param sheng 省名称（可为null）
     * @param shi 市名称（可为null）
     * @param qu 区县名称（可为null）
     * @param zhen 街镇名称（可为null）
     * @return 地物信息列表
     */
    List<TRegionInfo> selectByAdministrativeRegion(@Param("sheng") String sheng,
                                                   @Param("shi") String shi,
                                                   @Param("qu") String qu,
                                                   @Param("zhen") String zhen);

    /**
     * 根据ID查询中心点坐标
     * @param id 地物ID
     * @return 中心点坐标
     */
    Point selectCenterById(@Param("id") Integer id);

    /**
     * 根据ID查询区域范围
     * @param id 地物ID
     * @return 区域范围
     */
    MultiPolygon selectRegionById(@Param("id") Integer id);

    /**
     * 更新中心点坐标
     * @param id 地物ID
     * @param center 新的中心点坐标
     * @return 影响行数
     */
    int updateCenterById(@Param("id") Integer id, @Param("center") Point center);

    /**
     * 更新区域范围
     * @param id 地物ID
     * @param region 新的区域范围
     * @return 影响行数
     */
    int updateRegionById(@Param("id") Integer id, @Param("region") MultiPolygon region);

    /**
     * 更新业务字段（排除系统字段）
     * @param record 地物信息对象
     * @return 影响行数
     */
    @AutoFill(Operation.UPDATE)
    int updateBusinessFields(TRegionInfo record);

    /**
     * 查询所有地物信息（用于分页）
     * @return 地物信息列表
     */
    List<TRegionInfo> selectAll();

    /**
     * 查询地物信息及其关联的监测点列表
     * @return 地物信息与监测点关联查询结果列表
     */
    List<TRegionInfoWithMonitoringPointsDTO> selectRegionInfoWithMonitoringPoints();

    /**
     * 根据地物ID查询地物信息及其关联的监测点列表
     * @param regionId 地物ID
     * @return 地物信息与监测点关联查询结果
     */
    List<TRegionInfoWithMonitoringPointsDTO> selectRegionInfoWithMonitoringPointsById(@Param("regionId") Integer regionId);

    /**
     * 分页查询地物信息及其关联的监测点列表
     * @return 地物信息与监测点关联查询结果列表
     */
    List<TRegionInfoWithMonitoringPointsDTO> selectRegionInfoWithMonitoringPointsForPage();

    /**
     * 根据地物编码查询地物信息（排除指定ID）
     * @param code 地物编码
     * @param excludeId 要排除的地物ID（可为null）
     * @return 地物信息
     */
    TRegionInfo selectByCodeExcludeId(@Param("code") String code, @Param("excludeId") Integer excludeId);
}