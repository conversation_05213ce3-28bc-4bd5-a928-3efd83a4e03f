package com.lysjk.mapper;

import com.lysjk.entity.OperateLog;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 操作日志Mapper接口
 */
@Mapper
public interface OperateLogMapper {

    /**
     * 插入操作日志
     */
    @Insert("INSERT INTO operate_log (user_id, username, operation_name, operation_type, operation_url, operate_time, cost_time) " +
            "VALUES (#{userId}, #{username}, #{operationName}, #{operationType}, #{operationUrl}, #{operateTime}, #{costTime})")
    void insert(OperateLog log);

    /**
     * 根据主键删除操作日志
     */
    @Delete("DELETE FROM operate_log WHERE oid = #{oid}")
    int deleteByOid(Integer oid);

    /**
     * 分页查询操作日志
     */
    @Select("SELECT * FROM operate_log ORDER BY operate_time DESC")
    List<OperateLog> selectPage();

    /**
     * 根据条件查询操作日志（支持模糊查询）
     */
    @Select("<script>" +
            "SELECT * FROM operate_log WHERE 1=1 " +
            "<if test='username != null and username != \"\"'>" +
            "AND username LIKE CONCAT('%', #{username}, '%') " +
            "</if>" +
            "<if test='operationType != null and operationType != \"\"'>" +
            "AND operation_type LIKE CONCAT('%', #{operationType}, '%') " +
            "</if>" +
            "<if test='operationName != null and operationName != \"\"'>" +
            "AND operation_name LIKE CONCAT('%', #{operationName}, '%') " +
            "</if>" +
            "<if test='operationUrl != null and operationUrl != \"\"'>" +
            "AND operation_url LIKE CONCAT('%', #{operationUrl}, '%') " +
            "</if>" +
            "ORDER BY operate_time DESC" +
            "</script>")
    List<OperateLog> selectByCondition(@Param("username") String username,
                                       @Param("operationType") String operationType,
                                       @Param("operationName") String operationName,
                                       @Param("operationUrl") String operationUrl);
}
