package com.lysjk.mapper;

import com.lysjk.entity.Users;
import com.lysjk.vo.UsersVO;
import org.apache.ibatis.annotations.*;

import java.util.List;

@Mapper
public interface UsersMapper {
    @Select("select * from users where username = #{username}")
    Users selectByUsername(String username);

    @Select("select * from users where uid = #{uid}")
    Users selectByUid(Integer uid);

//    @Insert("insert into users(username, password, role, nickname, phone, email, create_dt, update_dt, last_login_time, default_pro) " +
//            "values (#{username}, #{password}, 'user', #{nickname}, #{phone}, #{email}, now(), now(), now(), #{defaultPro})")
    @Insert("insert into users(username, password, role, nickname, organization_id, create_dt, update_dt, last_login_time, default_pro) " +
        "values (#{username}, #{password}, 'user', #{nickname}, #{organizationId}, now(), now(), now(), #{defaultPro})")
    void createUser(String username, String password, String nickname, String defaultPro, Integer organizationId);

    @Update("update users set last_login_time = now() where uid = #{uid}")
    void loginTime(Integer uid);

    @Update("update users set nickname = #{nickname}, email = #{email}, phone = #{phone}, default_pro = #{defaultPro}, organization_id = #{organizationId}, update_dt = now() where uid = #{uid}")
    void update(Users users);

    @Update("update users set password = #{password}, update_dt = now() where uid = #{uid}")
    void updatePwdByUid(Integer uid, String password);

    @Delete("delete from users where uid = #{uid}")
    void deleteUser(Integer uid);

    @Select("select * from users order by uid asc")
    List<Users> selectAll();

    @Select("select * from users where username like concat('%', #{username}, '%') order by uid asc")
    List<Users> selectByUsernameLike(String username);

    List<UsersVO> list(String username, String nickname);

    /**
     * 这个就是测试一下excel,这时候肯定是批量新增的,其余比如可以多次添加口味也是批量新增
     * @param usersVOList
     * @param password
     * @param role
     */
    void saveBatch(List<UsersVO> usersVOList, String password, String role);
}