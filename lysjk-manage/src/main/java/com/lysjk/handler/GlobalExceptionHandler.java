package com.lysjk.handler;

import com.lysjk.exception.AuthenticationException;
import com.lysjk.exception.CustomException;
import com.lysjk.exception.ValidationException;
import com.lysjk.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import java.sql.SQLIntegrityConstraintViolationException;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 如果要返回标准化的错误响应,则需要对每个异常做单独处理,否则一个父异常即可完成需求。
 * HttpServletRequest 可以获得controller层的请求路径
 * TODO 像各种参数验证异常或绑定异常以后可以来处理,先暂时不管。
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理自定义业务异常
     */
    @ExceptionHandler(CustomException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result handleCustomException(CustomException e) {
        return Result.error(e.getCode(), e.getMsg());
    }

    /**
     * 处理权限不足异常
     */
    @ExceptionHandler(AuthenticationException.InsufficientPermissionException.class)
    @ResponseStatus(HttpStatus.FORBIDDEN)
    public Result handleInsufficientPermissionException(AuthenticationException.InsufficientPermissionException e) {
        return Result.error(e.getCode(), e.getMsg());
    }

    /**
     * 处理其他认证异常（如token无效、用户不存在等）
     */
    @ExceptionHandler(AuthenticationException.class)
    @ResponseStatus(HttpStatus.UNAUTHORIZED)
    public Result handleAuthenticationException(AuthenticationException e) {
        return Result.error(e.getCode(), e.getMsg());
    }

    /**
     * 处理数据验证异常
     */
    @ExceptionHandler(ValidationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result handleValidationException(ValidationException e) {
        return Result.error(e.getCode(), e.getMsg());
    }

    /**
     * 处理文件上传大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e) {
        return Result.error(413, "文件大小超过限制");
    }

    /**
     * 处理参数验证异常（@Valid注解触发）
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        return Result.error(400, "参数验证失败: " + errorMessage);
    }

    /**
     * 处理绑定异常
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result handleBindException(BindException e) {
        String errorMessage = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        return Result.error(400, "参数绑定失败: " + errorMessage);
    }

    /**
     * 处理空指针异常
     */
    @ExceptionHandler(NullPointerException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result handleNullPointerException(NullPointerException e) {
        return Result.error(500, "系统内部错误");
    }

    /**
     * 处理非法参数异常
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public Result handleIllegalArgumentException(IllegalArgumentException e) {
        return Result.error(400, "参数错误: " + e.getMessage());
    }

    /**
     * 处理sql异常(捕获就可以直接返回数据)
     * 主要是用于判断插入的数据某个字段需要唯一
     * @param ex
     * @return
     */
    @ExceptionHandler
    @ResponseStatus(HttpStatus.CONFLICT)
    public Result exceptionHandler(SQLIntegrityConstraintViolationException ex){
        String message = ex.getMessage(); // 当控制台出异常后可以将相应信息捕获(根据显示的信息下面进行操作)
        // 让前端可以更方便得提取错误信息
        if(message.contains("Duplicate entry")){
            String[] split = message.split(" ");
            String username = split[2];
            String msg = username + "已存在";
            return Result.error(409, msg);
        }else{
            return Result.error("sql发生未知错误");
        }
    }

    /**
     * 数据完整性异常
     * TODO: 这个应该可以统一捕获外键异常,比如关联就不能删除等。
     */
    @ExceptionHandler(DataIntegrityViolationException.class)
    @ResponseStatus(HttpStatus.CONFLICT)
    public Result handelDataIntegrityViolationException(DataIntegrityViolationException e) {

        if (e.getMessage().contains("foreign")) {
            return Result.error("无法删除，有其他数据引用");
        } else if (e.getMessage().contains("Duplicate")) {
            return Result.error("无法保存,数据已存在");
        }
        return Result.error("您的操作违反了数据库中的完整性约束,请联系管理员");
    }

    /**
     * 处理所有未捕获的异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public Result handleException(Exception e) {
        String message = StringUtils.hasLength(e.getMessage()) ? e.getMessage() : "系统内部发生未知错误";
        return Result.error(500, message);
    }
}

//package com.lysjk.handler;
//
//import com.lysjk.exception.AuthenticationException;
//import com.lysjk.exception.CustomException;
//import com.lysjk.exception.FileOperationException;
//import com.lysjk.exception.ValidationException;
//import com.lysjk.result.Result;
//import jakarta.servlet.http.HttpServletRequest;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.dao.DataIntegrityViolationException;
//import org.springframework.http.HttpStatus;
//import org.springframework.util.StringUtils;
//import org.springframework.validation.BindException;
//import org.springframework.validation.FieldError;
//import org.springframework.web.bind.MethodArgumentNotValidException;
//import org.springframework.web.bind.annotation.ExceptionHandler;
//import org.springframework.web.bind.annotation.ResponseStatus;
//import org.springframework.web.bind.annotation.RestControllerAdvice;
//import org.springframework.web.multipart.MaxUploadSizeExceededException;
//
//import java.sql.SQLIntegrityConstraintViolationException;
//import java.util.stream.Collectors;
//
///**
// * 全局异常处理器
// * 统一处理系统中的各种异常，返回标准化的错误响应
// */
//@Slf4j
//@RestControllerAdvice
//public class GlobalExceptionHandler {
//
//    /**
//     * 处理自定义业务异常
//     */
//    @ExceptionHandler(CustomException.class)
//    public Result handleCustomException(CustomException e, HttpServletRequest request) {
//        log.warn("业务异常 - URL: {}, 错误信息: {}", request.getRequestURI(), e.getMsg());
//        return Result.error(e.getCode(), e.getMsg());
//    }
//
//    /**
//     * 处理文件操作异常
//     */
//    @ExceptionHandler(FileOperationException.class)
//    public Result handleFileOperationException(FileOperationException e, HttpServletRequest request) {
//        log.error("文件操作异常 - URL: {}, 错误信息: {}", request.getRequestURI(), e.getMsg(), e);
//        return Result.error(e.getCode(), e.getMsg());
//    }
//
//    /**
//     * 处理权限不足异常
//     */
//    @ExceptionHandler(AuthenticationException.InsufficientPermissionException.class)
//    @ResponseStatus(HttpStatus.FORBIDDEN)
//    public Result handleInsufficientPermissionException(AuthenticationException.InsufficientPermissionException e, HttpServletRequest request) {
//        log.warn("权限不足异常 - URL: {}, 错误信息: {}", request.getRequestURI(), e.getMsg());
//        return Result.error(e.getCode(), e.getMsg());
//    }
//
//    /**
//     * 处理其他认证异常（如token无效、用户不存在等）
//     */
//    @ExceptionHandler(AuthenticationException.class)
//    @ResponseStatus(HttpStatus.UNAUTHORIZED)
//    public Result handleAuthenticationException(AuthenticationException e, HttpServletRequest request) {
//        log.warn("认证异常 - URL: {}, 错误信息: {}", request.getRequestURI(), e.getMsg());
//        return Result.error(e.getCode(), e.getMsg());
//    }
//
//    /**
//     * 处理数据验证异常
//     */
//    @ExceptionHandler(ValidationException.class)
//    @ResponseStatus(HttpStatus.BAD_REQUEST)
//    public Result handleValidationException(ValidationException e, HttpServletRequest request) {
//        log.warn("数据验证异常 - URL: {}, 错误信息: {}", request.getRequestURI(), e.getMsg());
//        return Result.error(e.getCode(), e.getMsg());
//    }
//
//    /**
//     * 处理参数验证异常（@Valid注解触发）
//     */
//    @ExceptionHandler(MethodArgumentNotValidException.class)
//    @ResponseStatus(HttpStatus.BAD_REQUEST)
//    public Result handleMethodArgumentNotValidException(MethodArgumentNotValidException e, HttpServletRequest request) {
//        String errorMessage = e.getBindingResult().getFieldErrors().stream()
//                .map(FieldError::getDefaultMessage)
//                .collect(Collectors.joining(", "));
//        log.warn("参数验证异常 - URL: {}, 错误信息: {}", request.getRequestURI(), errorMessage);
//        return Result.error(400, "参数验证失败: " + errorMessage);
//    }
//
//    /**
//     * 处理绑定异常
//     */
//    @ExceptionHandler(BindException.class)
//    @ResponseStatus(HttpStatus.BAD_REQUEST)
//    public Result handleBindException(BindException e, HttpServletRequest request) {
//        String errorMessage = e.getBindingResult().getFieldErrors().stream()
//                .map(FieldError::getDefaultMessage)
//                .collect(Collectors.joining(", "));
//        log.warn("绑定异常 - URL: {}, 错误信息: {}", request.getRequestURI(), errorMessage);
//        return Result.error(400, "参数绑定失败: " + errorMessage);
//    }
//
//    /**
//     * 处理文件上传大小超限异常
//     */
//    @ExceptionHandler(MaxUploadSizeExceededException.class)
//    @ResponseStatus(HttpStatus.BAD_REQUEST)
//    public Result handleMaxUploadSizeExceededException(MaxUploadSizeExceededException e, HttpServletRequest request) {
//        log.warn("文件上传大小超限 - URL: {}, 错误信息: {}", request.getRequestURI(), e.getMessage());
//        return Result.error(413, "文件大小超过限制");
//    }
//
//    /**
//     * 处理空指针异常
//     */
//    @ExceptionHandler(NullPointerException.class)
//    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
//    public Result handleNullPointerException(NullPointerException e, HttpServletRequest request) {
//        log.error("空指针异常 - URL: {}", request.getRequestURI(), e);
//        return Result.error(500, "系统内部错误");
//    }
//
//    /**
//     * 处理非法参数异常
//     */
//    @ExceptionHandler(IllegalArgumentException.class)
//    @ResponseStatus(HttpStatus.BAD_REQUEST)
//    public Result handleIllegalArgumentException(IllegalArgumentException e, HttpServletRequest request) {
//        log.warn("非法参数异常 - URL: {}, 错误信息: {}", request.getRequestURI(), e.getMessage());
//        return Result.error(400, "参数错误: " + e.getMessage());
//    }
//
//    /**
//     * 处理sql异常(捕获就可以直接返回数据,所以是200,也不会出错)
//     * 主要是用于判断插入的数据某个字段需要唯一
//     * @param ex
//     * @return
//     */
//    @ExceptionHandler
//    public Result exceptionHandler(SQLIntegrityConstraintViolationException ex){
//        String message = ex.getMessage(); // 当控制台出异常后可以将相应信息捕获(根据显示的信息下面进行操作)
//        // 让前端可以更方便得提取错误信息
//        if(message.contains("Duplicate entry")){
//            String[] split = message.split(" ");
//            String username = split[2];
//            String msg = username + "已存在";
//            return Result.error(msg);
//        }else{
//            return Result.error("未知错误");
//        }
//    }
//
//    /**
//     * 数据完整性异常
//     */
//    @ExceptionHandler(DataIntegrityViolationException.class)
//    public Result handelDataIntegrityViolationException(DataIntegrityViolationException e) {
//
//        if (e.getMessage().contains("foreign")) {
//            return Result.error("无法删除，有其他数据引用");
//        } else if (e.getMessage().contains("Duplicate")) {
//            return Result.error("无法保存,数据已存在");
//        }
//        return Result.error("您的操作违反了数据库中的完整性约束,请联系管理员");
//    }
//
//    /**
//     * 处理所有未捕获的异常
//     */
//    @ExceptionHandler(Exception.class)
//    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
//    public Result handleException(Exception e, HttpServletRequest request) {
//        log.error("系统异常 - URL: {}, 错误信息: {}", request.getRequestURI(), e.getMessage(), e);
//        String message = StringUtils.hasLength(e.getMessage()) ? e.getMessage() : "系统内部错误";
//        return Result.error(500, message);
//    }
//}