package com.lysjk.aspect;

import com.lysjk.anno.LogOperation;
import com.lysjk.entity.OperateLog;
import com.lysjk.entity.Users;
import com.lysjk.enums.OperationType;
import com.lysjk.mapper.OperateLogMapper;
import com.lysjk.mapper.UsersMapper;
import com.lysjk.utils.OperationNameUtil;
import com.lysjk.utils.ThreadLocalUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 操作日志切面
 */
@Slf4j
@Aspect
@Component
public class OperationLogAspect {
    @Autowired
    private OperateLogMapper operateLogMapper;
    @Autowired
    private UsersMapper usersMapper;

    @Around("@annotation(logOperation)")
    public Object around(ProceedingJoinPoint joinPoint, LogOperation logOperation) throws Throwable {
        long startTime = System.currentTimeMillis();
        String className = joinPoint.getTarget().getClass().getName();
        String methodName = joinPoint.getSignature().getName();

        OperateLog operateLog = new OperateLog();
        operateLog.setOperateTime(LocalDateTime.now());

        // 设置用户信息
        setUserInfo(operateLog, joinPoint);

        // 操作名称 & 类型 & URL
        operateLog.setOperationName(resolveOperationName(logOperation, className, methodName));
        operateLog.setOperationType(resolveOperationType(logOperation, methodName));
        operateLog.setOperationUrl(resolveRequestPath(joinPoint));

        Object result;
        boolean success = true;
        try {
            result = joinPoint.proceed();
        } catch (Throwable ex) {
            success = false;
            log.error("方法执行异常: {}.{}", className, methodName, ex);
            throw ex;
        } finally {
            operateLog.setCostTime((int)(System.currentTimeMillis() - startTime));
            if (success) {
                saveLogAsync(operateLog);
            }
        }
        return result;
    }

    private String resolveOperationName(LogOperation ann, String cls, String mtd) {
        return StringUtils.hasText(ann.operationName())
                ? ann.operationName()
                : OperationNameUtil.getOperationName(cls, mtd);
    }

    private String resolveOperationType(LogOperation ann, String mtd) {
        if (StringUtils.hasText(ann.operationType())) {
            return ann.operationType();
        }
        OperationType t = OperationType.inferFromMethodName(mtd);
        return t != null ? t.getCode() : OperationType.OTHER.getCode();
    }

    private void saveLogAsync(OperateLog logEntity) {
        try {
            operateLogMapper.insert(logEntity);
            log.info("操作日志保存成功: {}", logEntity.getOperationName());
        } catch (Exception e) {
            log.error("保存操作日志失败", e);
        }
    }

    private void setUserInfo(OperateLog operateLog, ProceedingJoinPoint joinPoint) {
        String path = resolveRequestPath(joinPoint);
        // 登录接口单独处理
        if (path != null && path.contains("/login")) {
            String username = extractUsernameFromArg(joinPoint.getArgs());
            if (username != null) {
                operateLog.setUsername(username);
                Users users = usersMapper.selectByUsername(username); // 查询不用设置事务
                operateLog.setUserId(users.getUid());
            } else {
                operateLog.setUserId(0);
                operateLog.setUsername("匿名登录");
            }
        } else {
            try {
                operateLog.setUserId(getCurrentUserId());
                operateLog.setUsername(getCurrentUserName());
            } catch (Exception ex) {
                operateLog.setUserId(0);
                operateLog.setUsername("用户未登录");
            }
        }
    }

    /**
     * 统一使用 BeanWrapper 提取 username 属性或 Map 中的键
     */
    private String extractUsernameFromArg(Object[] args) {
        if (args == null) return null;
        for (Object arg : args) {
            if (arg == null) continue;
            try {
                if (arg instanceof Map) {
                    Object u = ((Map<?, ?>) arg).get("username");
                    if (u != null) return u.toString();
                } else {
                    BeanWrapper bw = new BeanWrapperImpl(arg);
                    if (bw.isReadableProperty("username")) {
                        Object u = bw.getPropertyValue("username");
                        if (u != null) return u.toString();
                    }
                }
            } catch (Exception e) {
                log.debug("提取用户名失败，类型：{}，错误：{}", arg.getClass().getSimpleName(), e.getMessage());
            }
        }
        return null;
    }

   private String resolveRequestPath(ProceedingJoinPoint joinPoint) {
        try {
            Class<?> cls = joinPoint.getTarget().getClass();
            RequestMapping cm = cls.getAnnotation(RequestMapping.class);
            String base = (cm != null && cm.value().length > 0) ? cm.value()[0] : "";

            Method m = ((MethodSignature) joinPoint.getSignature()).getMethod();
            String sub = "";
            if (m.isAnnotationPresent(GetMapping.class)) {
                String[] value = m.getAnnotation(GetMapping.class).value();
                if(value != null && value.length > 0){
                    sub = value[0];
                }
            } else if (m.isAnnotationPresent(PostMapping.class)) {
                String[] value = m.getAnnotation(PostMapping.class).value();
                if(value != null && value.length > 0){
                    sub = value[0];
                }
            } else if (m.isAnnotationPresent(PutMapping.class)) {
                String[] value = m.getAnnotation(PutMapping.class).value();
                if(value != null && value.length > 0){
                    sub = value[0];
                }
            } else if (m.isAnnotationPresent(DeleteMapping.class)) {
                String[] value = m.getAnnotation(DeleteMapping.class).value();
                if(value != null && value.length > 0){
                    sub = value[0];
                }
            } else if (m.isAnnotationPresent(PatchMapping.class)) {
                String[] value = m.getAnnotation(PatchMapping.class).value();
                if(value != null && value.length > 0){
                    sub = value[0];
                }
            }
            return base + sub;
        } catch (Exception e) {
            log.debug("获取请求路径失败：{}", e.getMessage());
            return null;
        }
    }

    private Integer getCurrentUserId() {
        Map<String, Object> claims = ThreadLocalUtil.get();
        return claims == null ? null : (Integer) claims.get("uid");
    }

    private String getCurrentUserName() {
        Map<String, Object> claims = ThreadLocalUtil.get();
        return claims == null ? null : (String) claims.get("username");
    }
}


//package com.lysjk.aspect;
//
//import com.lysjk.anno.LogOperation;
//import com.lysjk.entity.OperateLog;
//import com.lysjk.enums.OperationType;
//import com.lysjk.mapper.OperateLogMapper;
//import com.lysjk.utils.OperationNameUtil;
//import com.lysjk.utils.ThreadLocalUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.aspectj.lang.ProceedingJoinPoint;
//import org.aspectj.lang.annotation.Around;
//import org.aspectj.lang.annotation.Aspect;
//import org.aspectj.lang.reflect.MethodSignature;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.util.StringUtils;
//import org.springframework.web.bind.annotation.*;
//
//import java.lang.reflect.Method;
//import java.time.LocalDateTime;
//import java.util.Map;
//
///**
// * 操作日志切面
// */
//@Slf4j
//@Aspect
//@Component
//public class OperationLogAspect {
//    @Autowired
//    private OperateLogMapper operateLogMapper;
//
//    /**
//     * 环绕通知，记录操作日志
//     * @param joinPoint 连接点
//     * @param logOperation 日志操作注解
//     * @return 方法执行结果
//     * @throws Throwable 异常
//     */
//    @Around("@annotation(logOperation)")
//    public Object around(ProceedingJoinPoint joinPoint, LogOperation logOperation) throws Throwable {
//        // 记录开始时间
//        long startTime = System.currentTimeMillis();
//
//        String className = joinPoint.getTarget().getClass().getName();
//        String methodName = joinPoint.getSignature().getName();
//
//        // 创建新的日志对象
//        OperateLog operateLog = new OperateLog();
//        operateLog.setOperateTime(LocalDateTime.now());
//
//        // 设置用户信息,因为登录成功后才获取到token,而注册时压根不获得token,不过只有管理员可以
//        setUserInfo(operateLog, joinPoint);
//
//        // 设置操作名称
//        String operationName = getOperationName(logOperation, className, methodName);
//        operateLog.setOperationName(operationName);
//
//        // 设置操作类型
//        String operationType = getOperationType(logOperation, methodName);
//        operateLog.setOperationType(operationType);
//
//        // 设置操作URL
//        String operationUrl = getRequestPath(joinPoint);
//        operateLog.setOperationUrl(operationUrl);
//
//        Object result = null;
//        boolean shouldSaveLog = true;
//
//        try {
//            // 执行目标方法
//            result = joinPoint.proceed();
//
//        } catch (Throwable throwable) {
//            log.error("方法执行异常: {}.{}", className, methodName, throwable);
//
//            // 发生异常就不记录了
//            shouldSaveLog = false;
//
//            throw throwable;
//
//        } finally {
//            // 计算执行时间
//            long endTime = System.currentTimeMillis();
//            operateLog.setCostTime((int)(endTime - startTime));
//
//            // 只有在非业务异常的情况下才保存日志,非异常才保存
//            if (shouldSaveLog) {
//                saveLogAsync(operateLog);
//            }
//        }
//
//        return result;
//    }
//
//    /**
//     * 获取操作名称
//     */
//    private String getOperationName(LogOperation logOperation, String className, String methodName) {
//        // 优先使用注解中指定的操作名称
//        if (StringUtils.hasText(logOperation.operationName())) {
//            return logOperation.operationName();
//        }
//
//        // 使用工具类推断操作名称
//        return OperationNameUtil.getOperationName(className, methodName);
//    }
//
//    /**
//     * 获取操作类型
//     */
//    private String getOperationType(LogOperation logOperation, String methodName) {
//        // 优先使用注解中指定的操作类型
//        if (StringUtils.hasText(logOperation.operationType())) {
//            return logOperation.operationType();
//        }
//
//        // 使用枚举推断操作类型
//        OperationType operationType = OperationType.inferFromMethodName(methodName);
//        return operationType.getCode();
//    }
//
//    /**
//     * 异步保存日志
//     */
//    private void saveLogAsync(OperateLog operateLog) {
//        try {
//            operateLogMapper.insert(operateLog);
//            log.info("操作日志保存成功: {}", operateLog.getOperationName());
//        } catch (Exception e) {
//            log.error("保存操作日志失败", e);
//        }
//    }
//
//    /**
//     * 设置用户信息
//     * 特别处理登录和注册接口的用户信息记录
//     */
//    private void setUserInfo(OperateLog operateLog, ProceedingJoinPoint joinPoint) {
//        String requestPath = getRequestPath(joinPoint);
//
//        // 对于登录接口，需要特殊处理用户信息
//        if (requestPath != null && (requestPath.contains("/login"))) {
//            // 登录接口，从参数中获取用户信息
//            Object[] args = joinPoint.getArgs();
//            if (args != null && args.length > 0) {
//                for (Object arg : args) {
//                    if (arg != null) {
//                        try {
//                            // 尝试从参数中提取用户名
//                            String username = extractUsernameFromArg(arg);
//                            if (username != null) {
//                                operateLog.setUsername(username);
//                                // 对于登录接口，如果登录成功，可以在后续设置userId
//                                // 这里先设置为null，登录成功后会在业务逻辑中更新
//                                operateLog.setUserId(null);
//                                return;
//                            }
//                        } catch (Exception e) {
//                            log.debug("从参数中提取用户名失败", e);
//                        }
//                    }
//                }
//            }
//            // 如果无法从参数提取，设置为匿名用户
//            operateLog.setUserId(0);
//            operateLog.setUsername("登录或注册失败");
//        } else {
//            // 其他接口，从ThreadLocal获取当前登录用户信息
//            try {
//                operateLog.setUserId(getCurrentUserId());
//                operateLog.setUsername(getCurrentUserName());
//            } catch (Exception e) {
//                // 用户未登录的情况
//                operateLog.setUserId(0);
//                operateLog.setUsername("用户未登录");
//            }
//        }
//    }
//
//    /**
//     * 从参数中提取用户名
//     */
//    private String extractUsernameFromArg(Object arg) {
//        if (arg == null) {
//            return null;
//        }
//
//        try {
//            // 如果是Map类型
//            if (arg instanceof Map) {
//                @SuppressWarnings("unchecked")
//                Map<String, Object> map = (Map<String, Object>) arg;
//                Object username = map.get("username");
//                return username != null ? username.toString() : null;
//            }
//
//            // 如果是Users对象，使用反射获取username字段
//            try {
//                java.lang.reflect.Field usernameField = arg.getClass().getDeclaredField("username");
//                usernameField.setAccessible(true);
//                Object username = usernameField.get(arg);
//                return username != null ? username.toString() : null;
//            } catch (NoSuchFieldException | IllegalAccessException e) {
//                // 字段不存在或无法访问，尝试其他方式
//            }
//
//            // 尝试调用getUsername方法
//            try {
//                Method getUsernameMethod = arg.getClass().getMethod("getUsername");
//                Object username = getUsernameMethod.invoke(arg);
//                return username != null ? username.toString() : null;
//            } catch (Exception e) {
//                // 方法不存在或调用失败
//            }
//
//        } catch (Exception e) {
//            log.debug("提取用户名失败", e);
//        }
//
//        return null;
//    }
//
//    /**
//     * 获取请求路径
//     */
//    private String getRequestPath(ProceedingJoinPoint joinPoint) {
//        try {
//            // 获取类上的RequestMapping注解
//            Class<?> targetClass = joinPoint.getTarget().getClass();
//            RequestMapping classMapping = targetClass.getAnnotation(RequestMapping.class);
//
//            String classPath = "";
//            if (classMapping != null && classMapping.value().length > 0) {
//                classPath = classMapping.value()[0];
//            }
//
//            // 获取方法上的映射注解
//            Method method = ((MethodSignature) joinPoint.getSignature()).getMethod();
//            String methodPath = "";
//
//            // 检查各种HTTP方法注解
//            if (method.isAnnotationPresent(GetMapping.class)) {
//                GetMapping mapping = method.getAnnotation(GetMapping.class);
//                if (mapping.value().length > 0) {
//                    methodPath = mapping.value()[0];
//                }
//            } else if (method.isAnnotationPresent(PostMapping.class)) {
//                PostMapping mapping = method.getAnnotation(PostMapping.class);
//                if (mapping.value().length > 0) {
//                    methodPath = mapping.value()[0];
//                }
//            } else if (method.isAnnotationPresent(PutMapping.class)) {
//                PutMapping mapping = method.getAnnotation(PutMapping.class);
//                if (mapping.value().length > 0) {
//                    methodPath = mapping.value()[0];
//                }
//            } else if (method.isAnnotationPresent(DeleteMapping.class)) {
//                DeleteMapping mapping = method.getAnnotation(DeleteMapping.class);
//                if (mapping.value().length > 0) {
//                    methodPath = mapping.value()[0];
//                }
//            } else if (method.isAnnotationPresent(PatchMapping.class)) {
//                PatchMapping mapping = method.getAnnotation(PatchMapping.class);
//                if (mapping.value().length > 0) {
//                    methodPath = mapping.value()[0];
//                }
//            }
//
//            return classPath + methodPath;
//        } catch (Exception e) {
//            log.debug("获取请求路径失败", e);
//            return null;
//        }
//    }
//
//    /**
//     * 获取当前用户ID
//     */
//    private Integer getCurrentUserId() {
//        Map<String, Object> claims = ThreadLocalUtil.get();
//        if (claims == null) {
//            return null;
//        }
//        return (Integer) claims.get("uid");
//    }
//
//    /**
//     * 获取当前用户名
//     */
//    private String getCurrentUserName() {
//        Map<String, Object> claims = ThreadLocalUtil.get();
//        if (claims == null) {
//            return null;
//        }
//        return (String) claims.get("username");
//    }
//}


/*====================================================================*/
    /*            // 判断是否为业务异常，如果是业务异常则不记录操作日志
            if (isBusinessException(throwable)) {
                shouldSaveLog = false;
                log.debug("检测到业务异常，跳过操作日志记录: {}", throwable.getMessage());
            }*/
//    /**
//     * 判断是否为业务异常
//     * 业务异常包括：CustomException及其子类（AuthenticationException、ValidationException、FileOperationException、BusinessException等）
//     */
//    private boolean isBusinessException(Throwable throwable) {
//        return throwable instanceof CustomException;
//    }