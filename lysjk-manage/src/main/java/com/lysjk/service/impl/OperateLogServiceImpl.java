package com.lysjk.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.lysjk.entity.OperateLog;
import com.lysjk.mapper.OperateLogMapper;
import com.lysjk.service.OperateLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 操作日志服务实现类
 */
@Slf4j
@Service
public class OperateLogServiceImpl implements OperateLogService {
    
    @Autowired
    private OperateLogMapper operateLogMapper;
    
    @Override
    public int deleteByOid(Integer oid) {
        return operateLogMapper.deleteByOid(oid);
    }

    @Override
    public PageInfo<OperateLog> selectPage(int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<OperateLog> list = operateLogMapper.selectPage();
        return PageInfo.of(list);
    }

    @Override
    public List<OperateLog> selectByCondition(String username, String operationType,
                                              String operationName, String operationUrl) {
        return operateLogMapper.selectByCondition(username, operationType, operationName, operationUrl);
    }

    @Override
    public PageInfo<OperateLog> selectPageByCondition(String username, String operationType,
                                                      String operationName, String operationUrl,
                                                      int pageNum, int pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<OperateLog> list = operateLogMapper.selectByCondition(username, operationType, operationName, operationUrl);
        return PageInfo.of(list);
    }
}
