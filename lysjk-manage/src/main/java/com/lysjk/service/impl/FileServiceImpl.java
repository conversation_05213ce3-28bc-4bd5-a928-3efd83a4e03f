package com.lysjk.service.impl;

import cn.hutool.core.util.IdUtil;
import com.lysjk.exception.FileOperationException;
import com.lysjk.exception.ValidationException;
import com.lysjk.service.FileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文件服务实现类
 */
@Slf4j
@Service
public class FileServiceImpl implements FileService {
    
    @Value("${upload.path}")
    private String uploadPath;
    
    @Value("${upload.max-file-size:10485760}") // 默认10MB
    private long maxFileSize;
    
    // 允许的文件类型
    private static final List<String> ALLOWED_EXTENSIONS = Arrays.asList(
            ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", // 图片
            ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", // 文档
            ".txt", ".csv", ".xml", ".json", // 文本
            ".zip", ".rar", ".7z", // 压缩包
            ".mp4", ".avi", ".mov", ".wmv", ".flv", // 视频
            ".mp3", ".wav", ".flac", ".aac" // 音频
    );
    
    // MIME类型映射
    private static final Map<String, String> MIME_TYPE_MAP = new HashMap<>();
    
    static {
        // 图片类型
        MIME_TYPE_MAP.put(".jpg", "image/jpeg");
        MIME_TYPE_MAP.put(".jpeg", "image/jpeg");
        MIME_TYPE_MAP.put(".png", "image/png");
        MIME_TYPE_MAP.put(".gif", "image/gif");
        MIME_TYPE_MAP.put(".bmp", "image/bmp");
        MIME_TYPE_MAP.put(".webp", "image/webp");
        
        // 文档类型
        MIME_TYPE_MAP.put(".pdf", "application/pdf");
        MIME_TYPE_MAP.put(".doc", "application/msword");
        MIME_TYPE_MAP.put(".docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        MIME_TYPE_MAP.put(".xls", "application/vnd.ms-excel");
        MIME_TYPE_MAP.put(".xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        MIME_TYPE_MAP.put(".ppt", "application/vnd.ms-powerpoint");
        MIME_TYPE_MAP.put(".pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation");
        
        // 文本类型
        MIME_TYPE_MAP.put(".txt", "text/plain");
        MIME_TYPE_MAP.put(".csv", "text/csv");
        MIME_TYPE_MAP.put(".xml", "application/xml");
        MIME_TYPE_MAP.put(".json", "application/json");
        
        // 压缩包类型
        MIME_TYPE_MAP.put(".zip", "application/zip");
        MIME_TYPE_MAP.put(".rar", "application/x-rar-compressed");
        MIME_TYPE_MAP.put(".7z", "application/x-7z-compressed");
        
        // 视频类型
        MIME_TYPE_MAP.put(".mp4", "video/mp4");
        MIME_TYPE_MAP.put(".avi", "video/x-msvideo");
        MIME_TYPE_MAP.put(".mov", "video/quicktime");
        MIME_TYPE_MAP.put(".wmv", "video/x-ms-wmv");
        MIME_TYPE_MAP.put(".flv", "video/x-flv");
        
        // 音频类型
        MIME_TYPE_MAP.put(".mp3", "audio/mpeg");
        MIME_TYPE_MAP.put(".wav", "audio/wav");
        MIME_TYPE_MAP.put(".flac", "audio/flac");
        MIME_TYPE_MAP.put(".aac", "audio/aac");
    }
    
    @Override
    public String uploadFile(MultipartFile file) {
        // 验证文件
        validateFile(file);
        
        // 确保上传目录存在
        createUploadDirectoryIfNotExists();
        
        // 生成唯一文件名
        String originalFilename = file.getOriginalFilename();
        String extension = getFileExtension(originalFilename);
        String newFileName = IdUtil.simpleUUID() + extension;
        
        // 保存文件
        try {
            Path filePath = Paths.get(uploadPath, newFileName);
            Files.copy(file.getInputStream(), filePath);
            log.info("文件上传成功: {}", filePath.toAbsolutePath());
            return newFileName;
        } catch (IOException e) {
            log.error("文件保存失败: {}", originalFilename, e);
            throw new FileOperationException.FileUploadException("文件保存失败", e);
        }
    }
    
    @Override
    public String getFilePath(String fileName) {
        return Paths.get(uploadPath, fileName).toAbsolutePath().toString();
    }
    
    @Override
    public boolean fileExists(String fileName) {
        Path filePath = Paths.get(uploadPath, fileName);
        return Files.exists(filePath) && Files.isRegularFile(filePath);
    }
    
    @Override
    public boolean deleteFile(String fileName) {
        try {
            Path filePath = Paths.get(uploadPath, fileName);
            return Files.deleteIfExists(filePath);
        } catch (IOException e) {
            log.error("删除文件失败: {}", fileName, e);
            return false;
        }
    }
    
    @Override
    public String getContentType(String fileName) {
        String extension = getFileExtension(fileName).toLowerCase();
        return MIME_TYPE_MAP.getOrDefault(extension, "application/octet-stream");
    }
    
    @Override
    public boolean isAllowedFileType(String fileName) {
        String extension = getFileExtension(fileName).toLowerCase();
        return ALLOWED_EXTENSIONS.contains(extension);
    }
    
    @Override
    public boolean isAllowedFileSize(long fileSize) {
        return fileSize <= maxFileSize;
    }
    
    /**
     * 验证上传的文件
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new ValidationException.ParameterNullException("上传文件");
        }
        
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.trim().isEmpty()) {
            throw new ValidationException.ParameterFormatException("文件名", "非空字符串");
        }
        
        // 检查文件名安全性
        if (originalFilename.contains("..") || originalFilename.contains("/") || originalFilename.contains("\\")) {
            throw new ValidationException.ParameterFormatException("文件名", "不能包含路径分隔符");
        }
        
        // 检查文件类型
        if (!isAllowedFileType(originalFilename)) {
            throw new FileOperationException.UnsupportedFileTypeException(getFileExtension(originalFilename));
        }
        
        // 检查文件大小
        if (!isAllowedFileSize(file.getSize())) {
            throw new FileOperationException.FileSizeExceededException(maxFileSize);
        }
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf("."));
    }
    
    /**
     * 创建上传目录（如果不存在）
     */
    private void createUploadDirectoryIfNotExists() {
        try {
            Path uploadDir = Paths.get(uploadPath);
            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
                log.info("创建上传目录: {}", uploadDir.toAbsolutePath());
            }
        } catch (IOException e) {
            log.error("创建上传目录失败: {}", uploadPath, e);
            throw new FileOperationException("创建上传目录失败", e);
        }
    }
}
