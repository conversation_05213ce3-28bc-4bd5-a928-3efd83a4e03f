package com.lysjk.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.lysjk.entity.TMonitoringPoint;
import com.lysjk.mapper.TMonitoringPointMapper;
import com.lysjk.service.TMonitoringPointService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 监测点信息服务实现类
 */
@Slf4j
@Service
@Transactional
public class TMonitoringPointServiceImpl implements TMonitoringPointService {

    @Autowired
    private TMonitoringPointMapper tMonitoringPointMapper;

    @Override
    public TMonitoringPoint selectByPrimaryKey(Integer id) {
        if (id == null) {
            throw new IllegalArgumentException("监测点ID不能为空");
        }
        log.info("根据ID查询监测点信息: {}", id);
        return tMonitoringPointMapper.selectByPrimaryKey(id);
    }

    @Override
    public int insertMonitoringPoint(TMonitoringPoint record) {
        if (record == null) {
            throw new IllegalArgumentException("监测点信息不能为空");
        }
        log.info("新增监测点信息: name={}, code={}, regionId={}",
                record.getName(), record.getCode(), record.getRegionId());
//        Map<String, Integer> claims = ThreadLocalUtil.get();
//        record.setCreateBy(claims.get("uid"));
//        record.setUpdateBy(claims.get("uid"));
        return tMonitoringPointMapper.insertBusinessFields(record);
    }

    @Override
    public int deleteByPrimaryKey(Integer id) {
        if (id == null) {
            throw new IllegalArgumentException("监测点ID不能为空");
        }
        log.info("删除监测点信息: {}", id);
        return tMonitoringPointMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int updateBusinessFields(TMonitoringPoint record) {
        if (record == null || record.getId() == null) {
            throw new IllegalArgumentException("监测点信息或ID不能为空");
        }
        log.info("更新监测点业务字段: id={}, name={}, code={}",
                record.getId(), record.getName(), record.getCode());
        return tMonitoringPointMapper.updateBusinessFields(record);
    }

    @Override
    public PageInfo<TMonitoringPoint> selectPage(int pageNum, int pageSize) {
        log.info("分页查询监测点信息 - 页码:{}, 每页大小:{}", pageNum, pageSize);

        // 参数验证
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1 || pageSize > 100) {
            pageSize = 10;
        }

        // 启动分页
        PageHelper.startPage(pageNum, pageSize);

        // 执行查询
        List<TMonitoringPoint> list = tMonitoringPointMapper.selectAll();

        // 封装分页结果
        PageInfo<TMonitoringPoint> pageInfo = PageInfo.of(list);

        log.info("分页查询完成 - 总记录数:{}, 总页数:{}, 当前页:{}",
                pageInfo.getTotal(), pageInfo.getPages(), pageInfo.getPageNum());

        return pageInfo;
    }

    @Override
    public List<TMonitoringPoint> selectByCondition(Integer regionId, String name, String code, String remark) {
        log.info("根据条件查询监测点 - regionId:{}, name:{}, code:{}, remark:{}",
                regionId, name, code, remark);
        return tMonitoringPointMapper.selectByCondition(regionId, name, code, remark);
    }

    @Override
    public PageInfo<TMonitoringPoint> selectPageByCondition(Integer regionId, String name, String code, String remark,
                                                          int pageNum, int pageSize) {
        log.info("分页条件查询监测点 - regionId:{}, name:{}, code:{}, remark:{}, 页码:{}, 每页大小:{}",
                regionId, name, code, remark, pageNum, pageSize);

        // 参数验证
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1 || pageSize > 100) {
            pageSize = 10;
        }

        // 启动分页
        PageHelper.startPage(pageNum, pageSize);

        // 执行查询
        List<TMonitoringPoint> list = tMonitoringPointMapper.selectByCondition(regionId, name, code, remark);

        // 封装分页结果
        PageInfo<TMonitoringPoint> pageInfo = PageInfo.of(list);

        log.info("分页条件查询完成 - 总记录数:{}, 总页数:{}, 当前页:{}",
                pageInfo.getTotal(), pageInfo.getPages(), pageInfo.getPageNum());

        return pageInfo;
    }

    @Override
    public List<TMonitoringPoint> selectByNameLike(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("查询名称不能为空");
        }
        log.info("根据名称模糊查询监测点: {}", name);
        return tMonitoringPointMapper.selectByNameLike(name.trim());
    }

    @Override
    public List<TMonitoringPoint> selectByLocationLike(String locationWkt) {
        if (locationWkt == null || locationWkt.trim().isEmpty()) {
            throw new IllegalArgumentException("查询位置不能为空");
        }
        log.info("根据地理位置模糊查询监测点: {}", locationWkt);
        return tMonitoringPointMapper.selectByLocationLike(locationWkt.trim());
    }

    @Override
    public List<TMonitoringPoint> selectByRegionId(Integer regionId) {
        if (regionId == null) {
            throw new IllegalArgumentException("地物ID不能为空");
        }
        log.info("根据地物ID查询监测点: {}", regionId);
        return tMonitoringPointMapper.selectByRegionId(regionId);
    }
}
