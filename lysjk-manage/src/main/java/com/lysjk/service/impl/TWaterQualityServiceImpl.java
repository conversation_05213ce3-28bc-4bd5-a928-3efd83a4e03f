package com.lysjk.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.lysjk.constant.RoleConstant;
import com.lysjk.dto.TWaterQualityPageQueryDTO;
import com.lysjk.entity.TWaterQuality;
import com.lysjk.exception.AuthenticationException;
import com.lysjk.exception.BusinessException;
import com.lysjk.exception.ValidationException;
import com.lysjk.mapper.TWaterQualityMapper;
import com.lysjk.result.PageResult;
import com.lysjk.service.TWaterQualityService;
import com.lysjk.utils.ThreadLocalUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 水质信息服务实现类
 */
@Slf4j
@Service
@Transactional
public class TWaterQualityServiceImpl implements TWaterQualityService {

    @Autowired
    private TWaterQualityMapper waterQualityMapper;

    /**
     * 新增水质信息
     * @param waterQuality 水质信息
     */
    @Override
    public void save(TWaterQuality waterQuality) {
        if (waterQuality == null) {
            throw new ValidationException.ParameterNullException("水质信息");
        }

        // 设置监测时间为当前时间（如果未设置）
        if (waterQuality.getMonitoringDt() == null) {
            waterQuality.setMonitoringDt(LocalDateTime.now());
        }

        log.info("新增水质信息: pointId={}, regionId={}, monitoringDt={}",
                waterQuality.getPointId(), waterQuality.getRegionId(), waterQuality.getMonitoringDt());

        int result = waterQualityMapper.insert(waterQuality);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("新增水质信息");
        }
    }

    /**
     * 批量删除水质信息
     * @param ids ID列表
     */
    @Override
    public void deleteBatch(List<Integer> ids) {
        if (ids == null || ids.isEmpty()) {
            throw new ValidationException.ParameterNullException("删除ID列表");
        }

        // 权限验证
        Map<String, Object> claims = ThreadLocalUtil.get();
        String role = (String) claims.get(RoleConstant.ROLE);
        if (!RoleConstant.ADMIN.equals(role)) {
            throw new AuthenticationException.InsufficientPermissionException("删除水质信息");
        }

        log.info("批量删除水质信息: {}", ids);

        int result = waterQualityMapper.deleteByIds(ids);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("删除水质信息");
        }
    }

    /**
     * 更新水质信息
     * @param waterQuality 水质信息
     */
    @Override
    public void update(TWaterQuality waterQuality) {
        if (waterQuality == null) {
            throw new ValidationException.ParameterNullException("水质信息");
        }

        if (waterQuality.getId() == null) {
            throw new ValidationException.ParameterNullException("水质信息ID");
        }

        log.info("更新水质信息: id={}, pointId={}, regionId={}",
                waterQuality.getId(), waterQuality.getPointId(), waterQuality.getRegionId());

        int result = waterQualityMapper.update(waterQuality);
        if (result <= 0) {
            throw new BusinessException.OperationFailedException("更新水质信息");
        }
    }

    /**
     * 分页查询水质信息
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    @Override
    public PageResult selectPage(TWaterQualityPageQueryDTO pageQueryDTO) {
        if (pageQueryDTO == null) {
            throw new ValidationException.ParameterNullException("分页查询条件");
        }

        log.info("分页查询水质信息: page={}, pageSize={}, pointId={}, regionId={}",
                pageQueryDTO.getPage(), pageQueryDTO.getPageSize(),
                pageQueryDTO.getPointId(), pageQueryDTO.getRegionId());

        // 开启分页
        PageHelper.startPage(pageQueryDTO.getPage(), pageQueryDTO.getPageSize());

        // 执行查询
        Page<TWaterQuality> page = waterQualityMapper.pageQuery(pageQueryDTO);

        // 封装结果
        long total = page.getTotal();
        List<TWaterQuality> result = page.getResult();

        log.info("分页查询完成，共查询到{}条记录", total);
        return new PageResult(total, result);
    }
}
