package com.lysjk.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.lysjk.dto.TRegionInfoPageQueryDTO;
import com.lysjk.dto.TRegionInfoWithMonitoringPointsDTO;
import com.lysjk.entity.TRegionInfo;
import com.lysjk.exception.ValidationException;
import com.lysjk.mapper.TRegionInfoMapper;
import com.lysjk.result.PageResult;
import com.lysjk.service.TRegionInfoService;
import com.lysjk.utils.ThreadLocalUtil;
import lombok.extern.slf4j.Slf4j;
import org.locationtech.jts.geom.Point;
import org.locationtech.jts.geom.MultiPolygon;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 地物信息服务实现类
 */
@Slf4j
@Service
@Transactional
public class TRegionInfoServiceImpl implements TRegionInfoService {
    
    @Autowired
    private TRegionInfoMapper tRegionInfoMapper;
    
    @Override
    public TRegionInfo selectByPrimaryKey(Integer id) {
        if (id == null) {
            throw new IllegalArgumentException("地物ID不能为空");
        }
        return tRegionInfoMapper.selectByPrimaryKey(id);
    }
    
    @Override
    public List<TRegionInfo> selectByAdministrativeRegion(String sheng, String shi, String qu, String zhen) {
        log.info("根据行政区划查询地物信息 - 省:{}, 市:{}, 区:{}, 镇:{}", sheng, shi, qu, zhen);
        return tRegionInfoMapper.selectByAdministrativeRegion(sheng, shi, qu, zhen);
    }
    
    @Override
    public Point selectCenterById(Integer id) {
        if (id == null) {
            throw new IllegalArgumentException("地物ID不能为空");
        }
        Point center = tRegionInfoMapper.selectCenterById(id);
        log.info("查询地物ID:{}的中心点坐标:{}", id, center != null ? center.toString() : "null");
        return center;
    }
    
    @Override
    public MultiPolygon selectRegionById(Integer id) {
        if (id == null) {
            throw new IllegalArgumentException("地物ID不能为空");
        }
        MultiPolygon region = tRegionInfoMapper.selectRegionById(id);
        log.info("查询地物ID:{}的区域范围:{}", id, region != null ? region.toString() : "null");
        return region;
    }
    
    @Override
    public int insert(TRegionInfo record) {
        if (record == null) {
            throw new IllegalArgumentException("地物信息不能为空");
        }
        
        // 设置创建信息
        setCreateInfo(record);
        
        int result = tRegionInfoMapper.insert(record);
        log.info("新增地物信息成功，ID:{}", record.getId());
        return result;
    }
    
    @Override
    public int insertSelective(TRegionInfo record) {
        if (record == null) {
            throw new IllegalArgumentException("地物信息不能为空");
        }
        
        // 设置创建信息
        setCreateInfo(record);
        
        int result = tRegionInfoMapper.insertSelective(record);
        log.info("选择性新增地物信息成功，ID:{}", record.getId());
        return result;
    }
    
    @Override
    public int deleteByPrimaryKey(Integer id) {
        if (id == null) {
            throw new IllegalArgumentException("地物ID不能为空");
        }
        
        // 检查记录是否存在
        TRegionInfo existing = tRegionInfoMapper.selectByPrimaryKey(id);
        if (existing == null) {
            throw new IllegalArgumentException("地物信息不存在，ID:" + id);
        }
        
        int result = tRegionInfoMapper.deleteByPrimaryKey(id);
        log.info("删除地物信息成功，ID:{}", id);
        return result;
    }
    
    @Override
    public int updateCenterById(Integer id, Point center) {
        if (id == null) {
            throw new IllegalArgumentException("地物ID不能为空");
        }
        if (center == null) {
            throw new IllegalArgumentException("中心点坐标不能为空");
        }
        
        // 检查记录是否存在
        TRegionInfo existing = tRegionInfoMapper.selectByPrimaryKey(id);
        if (existing == null) {
            throw new IllegalArgumentException("地物信息不存在，ID:" + id);
        }
        
        int result = tRegionInfoMapper.updateCenterById(id, center);
        log.info("更新地物ID:{}的中心点坐标成功:{}", id, center.toString());
        return result;
    }
    
    @Override
    public int updateRegionById(Integer id, MultiPolygon region) {
        if (id == null) {
            throw new IllegalArgumentException("地物ID不能为空");
        }
        if (region == null) {
            throw new IllegalArgumentException("区域范围不能为空");
        }
        
        // 检查记录是否存在
        TRegionInfo existing = tRegionInfoMapper.selectByPrimaryKey(id);
        if (existing == null) {
            throw new IllegalArgumentException("地物信息不存在，ID:" + id);
        }
        
        int result = tRegionInfoMapper.updateRegionById(id, region);
        log.info("更新地物ID:{}的区域范围成功:{}", id, region.toString());
        return result;
    }
    
    @Override
    public int updateBusinessFields(TRegionInfo record) {
        if (record == null || record.getId() == null) {
            throw new IllegalArgumentException("地物信息或ID不能为空");
        }
        
        // 检查记录是否存在
        TRegionInfo existing = tRegionInfoMapper.selectByPrimaryKey(record.getId());
        if (existing == null) {
            throw new IllegalArgumentException("地物信息不存在，ID:" + record.getId());
        }
        
        // 设置更新信息
        setUpdateInfo(record);
        
        int result = tRegionInfoMapper.updateBusinessFields(record);
        log.info("更新地物业务字段成功，ID:{}", record.getId());
        return result;
    }

    @Override
    public PageResult selectPage(TRegionInfoPageQueryDTO pageQueryDTO) {
        if (pageQueryDTO == null) {
            throw new ValidationException.ParameterNullException("分页查询条件");
        }

        log.info("分页查询地物信息: page={}, pageSize={}, name={}, code={}",
                pageQueryDTO.getPage(), pageQueryDTO.getPageSize(),
                pageQueryDTO.getName(), pageQueryDTO.getCode());

        // 开启分页
        PageHelper.startPage(pageQueryDTO.getPage(), pageQueryDTO.getPageSize());

        // 执行查询
        Page<TRegionInfo> page = tRegionInfoMapper.pageQuery(pageQueryDTO);

        // 封装结果
        long total = page.getTotal();
        List<TRegionInfo> result = page.getResult();

        log.info("分页查询完成，共查询到{}条记录", total);
        return new PageResult(total, result);
    }

    @Override
    public int updateByPrimaryKey(TRegionInfo record) {
        if (record == null || record.getId() == null) {
            throw new IllegalArgumentException("地物信息或ID不能为空");
        }
        
        // 设置更新信息
        setUpdateInfo(record);
        
        int result = tRegionInfoMapper.updateByPrimaryKey(record);
        log.info("全量更新地物信息成功，ID:{}", record.getId());
        return result;
    }
    
    /**
     * 设置创建信息
     */
    private void setCreateInfo(TRegionInfo record) {
        LocalDateTime now = LocalDateTime.now();
        record.setCreateDt(now);
        record.setUpdateDt(now);
        
        try {
            Map<String, Object> claims = ThreadLocalUtil.get();
            if (claims != null) {
                Integer userId = (Integer) claims.get("uid");
                record.setCreateBy(userId);
                record.setUpdateBy(userId);
            }
        } catch (Exception e) {
            log.warn("获取当前用户信息失败，使用默认值", e);
            // 如果获取用户信息失败，可以设置默认值或者抛出异常
        }
    }
    
    /**
     * 设置更新信息
     */
    private void setUpdateInfo(TRegionInfo record) {
        record.setUpdateDt(LocalDateTime.now());
        
        try {
            Map<String, Object> claims = ThreadLocalUtil.get();
            if (claims != null) {
                Integer userId = (Integer) claims.get("uid");
                record.setUpdateBy(userId);
            }
        } catch (Exception e) {
            log.warn("获取当前用户信息失败，使用默认值", e);
        }
    }

    @Override
    public List<TRegionInfoWithMonitoringPointsDTO> selectRegionInfoWithMonitoringPoints() {
        log.info("查询地物信息及其关联的监测点列表");
        List<TRegionInfoWithMonitoringPointsDTO> result = tRegionInfoMapper.selectRegionInfoWithMonitoringPoints();
        log.info("查询完成，共查询到{}条地物信息", result != null ? result.size() : 0);
        return result;
    }

    @Override
    public List<TRegionInfoWithMonitoringPointsDTO> selectRegionInfoWithMonitoringPointsById(Integer regionId) {
        if (regionId == null) {
            throw new IllegalArgumentException("地物ID不能为空");
        }
        log.info("根据地物ID查询地物信息及其关联的监测点列表: {}", regionId);
        List<TRegionInfoWithMonitoringPointsDTO> result = tRegionInfoMapper.selectRegionInfoWithMonitoringPointsById(regionId);
        if (result != null) {
            log.info("查询关联的内容成功");
        } else {
            log.info("未找到ID为{}的地物信息", regionId);
        }
        return result;
    }

    @Override
    public PageInfo<TRegionInfoWithMonitoringPointsDTO> selectRegionInfoWithMonitoringPointsPage(int pageNum, int pageSize) {
        log.info("分页查询地物信息及其关联的监测点列表 - 页码:{}, 每页大小:{}", pageNum, pageSize);

        // 参数验证
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1 || pageSize > 100) {
            pageSize = 10;
        }

        // 启动分页
        PageHelper.startPage(pageNum, pageSize);

        // 执行查询
        List<TRegionInfoWithMonitoringPointsDTO> list = tRegionInfoMapper.selectRegionInfoWithMonitoringPointsForPage();

        // 封装分页结果
        PageInfo<TRegionInfoWithMonitoringPointsDTO> pageInfo = PageInfo.of(list);

        log.info("分页查询完成 - 总记录数:{}, 总页数:{}, 当前页:{}",
                pageInfo.getTotal(), pageInfo.getPages(), pageInfo.getPageNum());

        return pageInfo;
    }

    @Override
    public boolean isCodeUnique(String code, Integer excludeId) {
        if (code == null || code.trim().isEmpty()) {
            throw new IllegalArgumentException("地物编码不能为空");
        }

        log.info("检查地物编码唯一性 - 编码:{}, 排除ID:{}", code, excludeId);

        TRegionInfo existing = tRegionInfoMapper.selectByCodeExcludeId(code.trim(), excludeId);
        boolean isUnique = existing == null;

        log.info("地物编码唯一性检查结果 - 编码:{}, 是否唯一:{}", code, isUnique);
        return isUnique;
    }
}
