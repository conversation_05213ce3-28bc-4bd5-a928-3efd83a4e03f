package com.lysjk.service;

import org.springframework.web.multipart.MultipartFile;

/**
 * 文件服务接口
 */
public interface FileService {
    
    /**
     * 上传文件
     * @param file 上传的文件
     * @return 文件保存后的名称
     */
    String uploadFile(MultipartFile file);
    
    /**
     * 获取文件的完整路径
     * @param fileName 文件名
     * @return 文件完整路径
     */
    String getFilePath(String fileName);
    
    /**
     * 检查文件是否存在
     * @param fileName 文件名
     * @return 是否存在
     */
    boolean fileExists(String fileName);
    
    /**
     * 删除文件
     * @param fileName 文件名
     * @return 是否删除成功
     */
    boolean deleteFile(String fileName);
    
    /**
     * 获取文件的MIME类型
     * @param fileName 文件名
     * @return MIME类型
     */
    String getContentType(String fileName);
    
    /**
     * 验证文件类型是否允许
     * @param fileName 文件名
     * @return 是否允许
     */
    boolean isAllowedFileType(String fileName);
    
    /**
     * 验证文件大小是否在允许范围内
     * @param fileSize 文件大小（字节）
     * @return 是否在允许范围内
     */
    boolean isAllowedFileSize(long fileSize);
}
