package com.lysjk.service;

import com.github.pagehelper.PageInfo;
import com.lysjk.entity.OperateLog;

import java.util.List;

/**
 * 操作日志服务接口
 */
public interface OperateLogService {

    /**
     * 根据主键删除操作日志
     */
    int deleteByOid(Integer oid);

    /**
     * 分页查询操作日志
     */
    PageInfo<OperateLog> selectPage(int pageNum, int pageSize);

    /**
     * 根据条件查询操作日志（支持模糊查询）
     */
    List<OperateLog> selectByCondition(String username, String operationType,
                                       String operationName, String operationUrl);

    /**
     * 分页条件查询操作日志
     */
    PageInfo<OperateLog> selectPageByCondition(String username, String operationType,
                                               String operationName, String operationUrl,
                                               int pageNum, int pageSize);
}
