package com.lysjk.service;

import com.lysjk.dto.TOrganizationInfoPageQueryDTO;
import com.lysjk.entity.TOrganizationInfo;
import com.lysjk.result.PageResult;

import java.util.List;

public interface TOrganizationInfoService {
    /**
     * 新增单位信息
     * @param organizationInfo
     */
    void save(TOrganizationInfo organizationInfo);

    /**
     * 批量删除操作
     * @param ids
     */
    void deleteBath(List<Long> ids);

    /**
     * 更新单位信息
     * @param organizationInfo
     */
    void update(TOrganizationInfo organizationInfo);

    /**
     * 根据名称条件分页查询
     * @param organizationInfoPageQueryDTO
     * @return
     */
    PageResult selectPage(TOrganizationInfoPageQueryDTO organizationInfoPageQueryDTO);
}
