package com.lysjk.service;

import com.lysjk.dto.TWaterQualityPageQueryDTO;
import com.lysjk.entity.TWaterQuality;
import com.lysjk.result.PageResult;

import java.util.List;

/**
 * 水质信息服务接口
 */
public interface TWaterQualityService {

    /**
     * 新增水质信息
     * @param waterQuality 水质信息
     */
    void save(TWaterQuality waterQuality);

    /**
     * 批量删除水质信息
     * @param ids ID列表
     */
    void deleteBatch(List<Integer> ids);

    /**
     * 更新水质信息
     * @param waterQuality 水质信息
     */
    void update(TWaterQuality waterQuality);

    /**
     * 分页查询水质信息
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    PageResult selectPage(TWaterQualityPageQueryDTO pageQueryDTO);
}
