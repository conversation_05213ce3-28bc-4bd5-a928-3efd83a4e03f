package com.lysjk.service;

import com.github.pagehelper.PageInfo;
import com.lysjk.entity.TMonitoringPoint;

import java.util.List;

/**
 * 监测点信息服务接口
 */
public interface TMonitoringPointService {

    /**
     * 根据主键查询监测点
     * @param id 监测点ID
     * @return 监测点信息
     */
    TMonitoringPoint selectByPrimaryKey(Integer id);

    /**
     * 新增监测点
     * @param record 监测点信息
     * @return 影响行数
     */
    int insertMonitoringPoint(TMonitoringPoint record);

    /**
     * 根据主键删除监测点
     * @param id 监测点ID
     * @return 影响行数
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 更新监测点业务字段
     * @param record 监测点信息
     * @return 影响行数
     */
    int updateBusinessFields(TMonitoringPoint record);

    /**
     * 分页查询监测点
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    PageInfo<TMonitoringPoint> selectPage(int pageNum, int pageSize);

    /**
     * 根据条件查询监测点
     * @param regionId 所属地物ID
     * @param name 监测点名称
     * @param code 监测点编号
     * @param remark 备注
     * @return 监测点列表
     */
    List<TMonitoringPoint> selectByCondition(Integer regionId, String name, String code, String remark);

    /**
     * 分页条件查询监测点
     * @param regionId 所属地物ID
     * @param name 监测点名称
     * @param code 监测点编号
     * @param remark 备注
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页结果
     */
    PageInfo<TMonitoringPoint> selectPageByCondition(Integer regionId, String name, String code, String remark,
                                                   int pageNum, int pageSize);

    /**
     * 根据名称模糊查询监测点
     * @param name 监测点名称关键字
     * @return 监测点列表
     */
    List<TMonitoringPoint> selectByNameLike(String name);

    /**
     * 根据地理位置模糊查询监测点
     * @param locationWkt 地理位置WKT字符串关键字
     * @return 监测点列表
     */
    List<TMonitoringPoint> selectByLocationLike(String locationWkt);

    /**
     * 根据地物ID查询监测点
     * @param regionId 地物ID
     * @return 监测点列表
     */
    List<TMonitoringPoint> selectByRegionId(Integer regionId);
}
