<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lysjk.mapper.UsersMapper">
  <insert id="saveBatch">
    insert into users (username, password, role, nickname, create_dt, update_dt, default_pro)
    values
    <foreach collection="usersVOList" item="item" separator=",">
      (#{item.username}, #{password}, #{role}, #{item.nickname}, now(), now(), #{item.defaultPro})
    </foreach>
  </insert>

  <select id="list" resultType="com.lysjk.vo.UsersVO">
    select * from users
    <where>
      <if test="username != null and username != ''">username like concat('%', #{username}, '%')</if>
      <if test="nickname != null and nickname != ''">and nickname like concat('%', #{nickname}, '%')</if>
    </where>
    order by uid asc
  </select>
</mapper>