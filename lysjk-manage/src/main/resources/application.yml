server:
  port: 8080

logging:
  level:
    com:
      lysjk:
        mapper: debug
        service: info
        controller: info
  pattern:
    console: '%d{HH:mm:ss.SSS} %clr(%-5level) ---  [%-15thread] %cyan(%-50logger{50}):%msg%n'

# 自动设置响应给前端的JsonFormat格式,不知道为啥不生效
spring:
  #  jackson:
  #    time-zone: GMT+8 # 设置时区(避免时间偏差)
  #    date-format: yyyy-MM-dd HH:mm:ss  # 指定日期格式
  mvc:
    static-path-pattern: /upload/**
  web:
    resources:
      static-locations: classpath:/upload/
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB
  profiles:
#    active: dev  # 默认开发环境，可通过启动参数覆盖
#    active: dev  # 开发环境
    active: prod  # 生产环境
#    active: docker  # docker环境

mybatis:
  mapper-locations: classpath:com/lysjk/mapper/*.xml
  type-aliases-package: com.lysjk.entity
  configuration:
    # 这个主要是可以在控制台查看sql详细信息
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
    # 启用自动映射
    auto-mapping-behavior: partial
    # 启用延迟加载
    lazy-loading-enabled: true
  # 注册自定义类型处理器
  type-handlers-package: com.lysjk.config.typehandler

pagehelper:
  reasonable: true
  helper-dialect: postgresql
  support-methods-arguments: true
  params: count=countSql
# 文件上传配置
upload:
  path: ./upload/
  max-file-size: 10485760  # 10MB
  allowed-extensions:
    - .jpg
    - .jpeg
    - .png
    - .gif
    - .bmp
    - .webp
    - .pdf
    - .doc
    - .docx
    - .xls
    - .xlsx
    - .ppt
    - .pptx
    - .txt
    - .csv
    - .xml
    - .json
    - .zip
    - .rar
    - .7z
    - .mp4
    - .avi
    - .mov
    - .wmv
    - .flv
    - .mp3
    - .wav
    - .flac
    - .aac