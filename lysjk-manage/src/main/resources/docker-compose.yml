# docker-compose.yml - 修正版本
version: '3.8'

services:
  # Spring Boot 应用服务
  app:
    image: backend:1.0
    container_name: spring-app
    ports:
      - "88:8080"
    environment:
      # 数据库配置 - 确保与PostgreSQL容器配置一致
      SPRING_DATASOURCE_URL: ************************************
      SPRING_DATASOURCE_USERNAME: postgres
      SPRING_DATASOURCE_PASSWORD: 'wyh123456'
      # Redis配置
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
      SPRING_REDIS_PASSWORD: "myredispass"
      SPRING_PROFILES_ACTIVE: dev
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
    restart: unless-stopped
    volumes:
      - ./upload:/app/upload  # 文件上传目录映射

  # PostgreSQL数据库服务
  postgres:
    image: postgis/postgis:17-3.4
    container_name: postgres
    environment:
      POSTGRES_DB: test  # 修改为与application.yml一致
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 'wyh123456' # 修改为与application.yml一致
    volumes:
      - pgdata:/var/lib/postgresql/data
    ports:
      - "54:5432"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis缓存服务
  redis:
    image: redis:latest
    container_name: redis
    command: redis-server --requirepass myredispass
    volumes:
      - redisdata:/data
    ports:
      - "63:6379"
    restart: unless-stopped

volumes:
  pgdata:
  redisdata:
