# 系统运行模式

## 项目概述

本项目是基于Spring Boot 3.2.0的多模块企业级后端系统，采用Maven多模块架构，包含lysjk-common（公共工具模块）、lysjk-pojo（实体类模块）和lysjk-manage（管理服务模块）三个核心模块。系统集成了PostgreSQL数据库（支持PostGIS地理信息扩展）、Redis缓存、JWT认证、文件上传等功能。

## 一、JAR包运行模式

### 适用场景
- 生产环境部署（服务器、云平台）
- 开发环境快速启动
- 单机部署场景

### 操作方式

#### 1. 项目打包
```bash
# 在项目根目录执行(加上-DskipTests可以跳过test下的测试代码)
mvn clean package 
# 打包完成后，可执行JAR文件位于：
# lysjk-manage/target/lysjk-manage-1.0-SNAPSHOT.jar
```

#### 2. 启动应用
```bash
# 基础启动（使用默认配置）
java -jar lysjk-manage/target/lysjk-manage-1.0-SNAPSHOT.jar

# 指定环境配置启动
java -jar lysjk-manage/target/lysjk-manage-1.0-SNAPSHOT.jar --spring.profiles.active=prod
```

#### 3. 环境配置说明
- **开发环境(dev)**: 连接本地PostgreSQL(localhost:5432)和Redis(localhost:6379)
- **生产环境(prod)**: 连接生产PostgreSQL和Redis
- **Docker环境(docker)**: 使用环境变量配置，适配容器化部署

### 优势
- 内嵌Tomcat服务器，无需外部容器
- 独立运行，资源占用少
- 配置灵活，支持多环境切换
- 适合容器化部署

## 二、前端资源集成部署

### 前端集成步骤

#### 1. 前端项目打包
```bash
# Vue.js项目
npm run build
# 或 React项目
npm run build
```

#### 2. 集成到Spring Boot
```bash
# 将前端dist目录内容复制到Spring Boot静态资源目录
cp -r frontend/dist/* lysjk-manage/src/main/resources/static/

# 重新打包
mvn clean package
```

### 优势
- 单JAR包部署，简化运维
- 减少跨域问题
- 统一端口访问
- 便于负载均衡配置

## 三、数据库部署方式

### 1. Docker容器部署（推荐）

#### PostgreSQL + PostGIS容器
```yaml
postgres:
  image: postgis/postgis:17-3.4
  container_name: postgres
  environment:
    POSTGRES_DB: test
    POSTGRES_USER: postgres
    POSTGRES_PASSWORD: 'wyh123456'
  volumes:
    - /data/pgdata_v1_0:/var/lib/postgresql/data
  ports:
    - "54:5432"
  restart: unless-stopped
```

#### Redis容器
```yaml
redis:
  image: redis:latest
  container_name: redis
  command: redis-server --requirepass myredispass
  volumes:
    - /data/redisdata_v1_0:/data
  ports:
    - "63:6379"
  restart: unless-stopped
```

### 2. Windows本地部署

#### PostgreSQL安装
1. 下载PostgreSQL 17安装包
2. 安装时选择PostGIS扩展
3. 创建数据库：`CREATE DATABASE test;`
4. 启用PostGIS：`CREATE EXTENSION postgis;`

#### Redis安装
1. 下载Redis for Windows
2. 配置密码：`requirepass root@123456`
3. 启动服务：`redis-server.exe redis.conf`

## 四、Docker容器化运行

### 1. 应用镜像构建

#### Dockerfile示例
```dockerfile
FROM openjdk:17-jre-slim

WORKDIR /app

COPY lysjk-manage/target/lysjk-manage-1.0-SNAPSHOT.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "app.jar"]
```

#### 构建命令
```bash
# 构建应用镜像
docker build -t backend:1.1 .
```

### 2. Docker Compose部署

#### 完整服务编排
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f app
```

#### 服务配置说明
- **应用服务**: 端口映射88:8080，依赖数据库健康检查
- **数据库服务**: PostGIS支持，数据持久化
- **缓存服务**: Redis密码认证，数据持久化
- **网络通信**: 容器间通过服务名通信

### 3. 环境变量配置
```bash
# 数据库配置
SPRING_DATASOURCE_URL=************************************
SPRING_DATASOURCE_USERNAME=postgres
SPRING_DATASOURCE_PASSWORD=wyh123456

# Redis配置
SPRING_REDIS_HOST=redis
SPRING_REDIS_PORT=6379
SPRING_REDIS_PASSWORD=myredispass

# 激活Docker环境配置
SPRING_PROFILES_ACTIVE=docker
```

### 优势
- 环境一致性保证
- 快速部署和扩展
- 服务隔离和依赖管理
- 便于CI/CD集成

## 五、系统部署运行总结

​	本系统采用现代化的前后端分离架构设计，支持多种部署模式，具有高度的灵活性和可扩展性。在技术选型上，我们选择了Spring Boot 3.2.0作为核心框架，配合PostgreSQL数据库（集成PostGIS地理信息扩展）和Redis缓存，构建了一个功能完整的企业级后端系统。

​	系统的部署策略体现了从传统部署到容器化部署的演进过程。在开发阶段，可以使用JAR包模式快速启动和调试，通过Maven多模块管理确保代码结构清晰、依赖关系明确。生产环境部署时，推荐使用Docker容器化方案，通过docker-compose编排PostgreSQL、Redis和应用服务，实现一键部署和服务依赖管理。

​	数据库部署方面，系统同时支持Docker容器化部署和Windows本地部署两种方式。Docker方式使用官方postgis/postgis镜像，自动集成PostGIS扩展，数据持久化通过卷映射实现；Windows本地部署则需要手动安装PostgreSQL和PostGIS扩展，适合开发环境使用。		        

​	Redis缓存同样支持容器化和本地部署，通过密码认证确保数据安全。

​	前端资源集成是本系统的一大特色，通过Spring Boot的静态资源映射机制，可以将Vue.js或React等前端框架打包后的静态文件直接集成到JAR包中，实现前后端一体化部署。这种方式不仅简化了部署流程，减少了跨域问题，还便于负载均衡和反向代理配置。

​	总的来说，本系统的部署架构充分考虑了现代应用的部署需求，从单机部署到容器化集群部署都有完整的解决方案，既保证了开发效率，又满足了生产环境的高可用性和可扩展性要求。通过Docker容器化技术，实现了"一次构建，到处运行"的目标，大大降低了运维成本和部署复杂度。
