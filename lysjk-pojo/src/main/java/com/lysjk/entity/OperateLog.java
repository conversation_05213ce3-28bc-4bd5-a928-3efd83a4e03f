package com.lysjk.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 操作日志实体类
 * 目前8个字段(已完善)
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OperateLog implements Serializable {
    private Integer oid; // 主键ID自增
    private Integer userId; // 操作人ID
    private String username; // 操作账号
    private String operationName; // 操作名称（如"用户登录"）
    private String operationType; // 操作类型（如"SELECT"、"INSERT"）
    private String operationUrl; // 操作的请求路径url
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime operateTime; // 操作的具体时间
    private Integer costTime; // 方法执行耗时(ms)
}
