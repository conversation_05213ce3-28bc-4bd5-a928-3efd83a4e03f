package com.lysjk.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * users
 * 目前12个字段(已完善)
 */
@Data
public class Users implements Serializable {
    private Integer uid;
    private String username;
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY) // 允许前端将密码传到后端,但是不允许后端传给前端
    private String password;
    private String role; // 只能赋值为管理员或用户
    private String nickname;
    private String phone;
    private String email;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDt; // 创建时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateDt; // 更新时间
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastLoginTime; // 最后更新时间
    private String defaultPro; // 默认项目
    private Integer organizationId; // 所属监测单位ID
    private static final long serialVersionUID = 1L;
}
