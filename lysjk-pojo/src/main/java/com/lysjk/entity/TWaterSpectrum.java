package com.lysjk.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 波谱信息表
 * t_water_spectrum
 * 目前11个字段(较为完善)
 */
@Data
public class TWaterSpectrum implements Serializable {
    /**
     * 唯一ID
     */
    private Integer id;

    /**
     * 监测点ID
     */
    private Integer pointId;

    /**
     * 所属地物ID
     */
    private Integer regionId;

    /**
     * 监测时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime monitoringDt;

    /**
     * 波谱数据
     * 存储JSON格式的波谱数据，如：{"400":0.005529024,"401":0.005508018,...}
     * 使用Map类型方便前端处理数据，数据库存储为json格式
     */
    private Map<String, Double> spectrum;

    /**
     * 记录创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDt;

    /**
     * 记录创建者ID
     */
    private Integer createBy;

    /**
     * 记录修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateDt;

    /**
     * 记录修改者ID
     */
    private Integer updateBy;

    /**
     * 备注
     */
    private String remark;

    /**
     * 监测单位
     */
    private Integer orgnizationId;

    private static final long serialVersionUID = 1L;
}