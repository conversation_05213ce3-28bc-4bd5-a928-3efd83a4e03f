package com.lysjk.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 影像信息表
 * t_image
 * 目前13个字段(很多字段填写不太确定)
 */
@Data
public class TImage implements Serializable {
    /**
     * 唯一ID
     */
    private Integer id;

    /**
     * 影像名称
     */
    private String name;

    /**
     * 影像获取时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    private LocalDateTime acquisitionDt;

    /**
     * 传感器类型
     */
    private String sensorType;

    /**
     * 存储路径
     */
    private String filePath;

    /**
     * 元数据
     */
    private String metaData;

    /**
     * 记录创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDt;

    /**
     * 记录创建者ID
     */
    private Integer createBy;

    /**
     * 记录修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateDt;

    /**
     * 记录修改者ID
     */
    private Integer updateBy;

    /**
     * 备注
     */
    private String remark;

    /**
     * 所属地物ID
     */
    private Integer regionId;

    /**
     * 影像边界范围
     */
    private Object bounds;

    private static final long serialVersionUID = 1L;
}