package com.lysjk.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.lysjk.config.serializer.PointDeserializer;
import com.lysjk.config.serializer.PointSerializer;
import lombok.Data;
import org.locationtech.jts.geom.Point;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 监测点信息表
 * t_monitoring_point
 * 目前10个字段(基本完善)
 */
@Data
public class TMonitoringPoint implements Serializable {
    /**
     * 唯一ID
     */
    private Integer id;

    /**
     * 所属地物ID
     */
    private Integer regionId;

    /**
     * 监测点名称
     */
    private String name;

    /**
     * 地理坐标 - PostGIS POINT类型
     */
    @JsonSerialize(using = PointSerializer.class)
    @JsonDeserialize(using = PointDeserializer.class)
    private Point location;

    /**
     * 监测点编号
     */
    private String code;

    /**
     * 记录创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDt;

    /**
     * 记录创建者ID
     */
    private Integer createBy;

    /**
     * 记录修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateDt;

    /**
     * 记录修改者ID
     */
    private Integer updateBy;

    /**
     * 备注
     */
    private String remark;

    private static final long serialVersionUID = 1L;
}