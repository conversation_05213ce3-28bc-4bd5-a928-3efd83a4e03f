package com.lysjk.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 单位信息表
 * t_organization_info
 * 正常7个字段
 */
@Data
public class TOrganizationInfo {
    private Integer id; // 唯一ID主键自增
    private String name; // 单位名称 必须唯一
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDt; // 创建时间
    private Integer createBy; // 创建人uid
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateDt; // 修改时间
    private Integer updateBy; // 修改人uid
    private String remark; // 备注可以为null
}
