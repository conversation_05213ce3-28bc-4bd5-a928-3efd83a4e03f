package com.lysjk.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 地物信息分页查询DTO
 */
@Data
public class TRegionInfoPageQueryDTO implements Serializable {

    //页码(先默认设值)
    private int page = 1;

    //每页显示记录数
    private int pageSize = 10;

    /**
     * 地物编码
     */
    private String code;

    /**
     * 地物名称
     */
    private String name;

    /**
     * 省名称
     */
    private String sheng;

    /**
     * 市名称
     */
    private String shi;

    /**
     * 区县名称
     */
    private String qu;

    /**
     * 街镇名称
     */
    private String zhen;

    /**
     * 坐落位置
     */
    private String zuoluo;

    /**
     * 监测单位ID
     */
    private Integer organizationId;

    /**
     * 是否包含 1:包含 0:未包含
     */
    private Integer status;

    /**
     * 所属河系
     */
    private String river;

    /**
     * 编号
     */
    private String number;

    /**
     * 采集方法
     */
    private String method;

    /**
     * 采集地点
     */
    private String location;

    /**
     * 行政编码
     */
    private String administrativeCode;

    /**
     * 采样时间开始
     */
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd")
    private LocalDateTime sampleDtStart;

    /**
     * 采样时间结束
     */
    @JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy/MM/dd")
    private LocalDateTime sampleDtEnd;

    private static final long serialVersionUID = 1L;
}
