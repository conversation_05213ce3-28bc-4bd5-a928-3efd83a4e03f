package com.lysjk.dto;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 波谱信息分页查询DTO
 */
@Data
public class TWaterSpectrumPageQueryDTO implements Serializable {

    //页码(先默认设值)
    private int page = 1;

    //每页显示记录数
    private int pageSize = 10;

    /**
     * 监测点ID
     */
    private Integer pointId;

    /**
     * 所属地物ID
     */
    private Integer regionId;

    /**
     * 监测单位ID
     */
    private Integer orgnizationId;

    /**
     * 监测时间开始
     */
    private LocalDateTime monitoringDtStart;

    /**
     * 监测时间结束
     */
    private LocalDateTime monitoringDtEnd;

    private static final long serialVersionUID = 1L;
}
