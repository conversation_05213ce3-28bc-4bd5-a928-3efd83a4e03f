package com.lysjk.dto;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;

/**
 * 文件上传DTO
 */
@Data
public class FileUploadDTO implements Serializable {
    
    @NotNull(message = "上传文件不能为空")
    private MultipartFile file;
    
    /**
     * 文件描述
     */
    private String description;
    
    /**
     * 文件分类
     */
    private String category;
}
