package com.lysjk.dto;

import com.lysjk.entity.TMonitoringPoint;
import com.lysjk.entity.TRegionInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 地物信息与监测点关联查询DTO
 * 用于承载地物信息及其关联的监测点列表
 */
@Data
public class TRegionInfoWithMonitoringPointsDTO implements Serializable {
    
    /**
     * 地物信息
     */
    private TRegionInfo regionInfo;
    
    /**
     * 关联的监测点列表
     */
    private List<TMonitoringPoint> monitoringPoints;
    
    /**
     * 监测点数量
     */
    private Integer monitoringPointCount;
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 构造函数
     */
    public TRegionInfoWithMonitoringPointsDTO() {
    }
    
    /**
     * 构造函数
     * @param regionInfo 地物信息
     * @param monitoringPoints 监测点列表
     */
    public TRegionInfoWithMonitoringPointsDTO(TRegionInfo regionInfo, List<TMonitoringPoint> monitoringPoints) {
        this.regionInfo = regionInfo;
        this.monitoringPoints = monitoringPoints;
        this.monitoringPointCount = monitoringPoints != null ? monitoringPoints.size() : 0;
    }
    
    /**
     * 设置监测点列表并自动计算数量
     * @param monitoringPoints 监测点列表
     */
    public void setMonitoringPoints(List<TMonitoringPoint> monitoringPoints) {
        this.monitoringPoints = monitoringPoints;
        this.monitoringPointCount = monitoringPoints != null ? monitoringPoints.size() : 0;
    }
}
